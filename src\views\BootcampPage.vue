<template>
  <div>
    <HeroSection
      badge-text="Bootcamp"
      title-text="Boost Your Tech Career with Our Intensive Bootcamps!"
      subtitle-text="Master Back-End, Front-End, and Mobile App Development with hands-on projects and expert mentorship"
      description-text="Ready to grow?"
      whatsappText="Consultation With Us"
      :hero-image="bootcampHero"
      :use-overlay="false"
      :is-dark="false"
      :is-detail-page="false"
      :show-get-started="true"
    />

    <!-- Custom Why Choose Section -->
    <section class="bg-[#ff8c00] pt-8">
      <div class="bg-white rounded-t-[30px] relative overflow-hidden pb-16">
        <div class="container mx-auto px-4">
          <!-- Success Stats Header -->
          <div class="text-center mb-12 pt-12">
            <h2 class="font-bold text-5xl">Our Success</h2>
            <p class="text-gray-600 text-lg">Join thousands of successful graduates who have transformed their careers through our industry-leading bootcamp programs.</p>
          </div>

          <div class="grid grid-cols-1 md:grid-cols-12 gap-4">
            <!-- Left Content -->
            <div class="md:col-span-7 px-4 pb-5">
              <div class="py-8 relative z-10">
                <!-- Stats Grid -->
                <div class="grid grid-cols-2 gap-4">
                  <!-- Students -->
                  <div>
                    <div class="text-center p-4 bg-white rounded-xl shadow-sm">
                      <h3 class="font-bold text-[#F2720C] text-4xl mb-2">10.000+</h3>
                      <p class="text-gray-600 font-medium">Students</p>
                    </div>
                  </div>

                  <!-- Success Rate -->
                  <div>
                    <div class="text-center p-4 bg-white rounded-xl shadow-sm">
                      <h3 class="font-bold text-[#F2720C] text-4xl mb-2">90%</h3>
                      <p class="text-gray-600 font-medium">Total Success</p>
                    </div>
                  </div>

                  <!-- Mentors -->
                  <div>
                    <div class="text-center p-4 bg-white rounded-xl shadow-sm">
                      <h3 class="font-bold text-[#F2720C] text-4xl mb-2">250+</h3>
                      <p class="text-gray-600 font-medium">Mentors</p>
                    </div>
                  </div>

                  <!-- Years Experience -->
                  <div>
                    <div class="text-center p-4 bg-white rounded-xl shadow-sm">
                      <h3 class="font-bold text-[#F2720C] text-4xl mb-2">17</h3>
                      <p class="text-gray-600 font-medium">Years Experience</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- Right Content -->
            <div class="md:col-span-5 relative flex items-center justify-center">
              <!-- Student Image with Badges -->
              <div class="relative h-[400px] w-full mb-24 z-10 md:h-[300px]">
                <StudentImageSection
                  :student-image="studentImage"
                  image-alt="Student"
                  video-badge-count="250K"
                  video-badge-text="Video Courses"
                  student-badge-count="250+"
                  student-badge-text="Assisted Students"
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Skills Section -->
    <section class="relative py-16 overflow-visible">
      <div class="absolute inset-0 bg-[#F2720CDB] h-[70%] md:h-[65%]"></div>
      <div class="container mx-auto px-4 relative z-10">
        <div class="text-center mb-12">
          <h1 class="text-5xl font-bold text-white mb-6">
            Power Up Your Skills: Why Join Us?
          </h1>
          <p class="text-[#006D77] text-lg">
            Our bootcamps are designed to help you master in-demand skills and launch your career in tech with confidence and expertise.
          </p>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
          <!-- Real-world Curriculum -->
          <div class="bg-white rounded-lg p-8 shadow-lg h-full relative">
            <div class="flex justify-center -mt-16 mb-6">
              <div class="w-16 h-16 rounded-full bg-[#6C63FF] flex items-center justify-center shadow-md">
                <img src="/invoice.png" alt="Real-world Curriculum" class="h-8 w-8" />
              </div>
            </div>
            <h3 class="text-xl font-semibold text-[#006D77] text-center mb-4">
              Real-world Curriculum
            </h3>
            <p class="text-gray-600 text-center">
              Our curriculum is designed by industry experts to teach you practical, job-ready skills through hands-on projects and real-world applications.
            </p>
          </div>

          <!-- Career Support -->
          <div class="bg-white rounded-lg p-8 shadow-lg h-full relative">
            <div class="flex justify-center -mt-16 mb-6">
              <div class="w-16 h-16 rounded-full bg-[#F2720C] flex items-center justify-center shadow-md">
                <img src="/certificate.png" alt="Career Support" class="h-8 w-8" />
              </div>
            </div>
            <h3 class="text-xl font-semibold text-[#006D77] text-center mb-4">
              Career Support & Certification
            </h3>
            <p class="text-gray-600 text-center">
              Get personalized career guidance, job placement assistance, and industry-recognized certifications to help you land your dream tech job.
            </p>
          </div>

          <!-- Expert Mentorship -->
          <div class="bg-white rounded-lg p-8 shadow-lg h-full relative">
            <div class="flex justify-center -mt-16 mb-6">
              <div class="w-16 h-16 rounded-full bg-[#00BCD4] flex items-center justify-center shadow-md">
                <img src="/user.png" alt="Expert Mentorship" class="h-8 w-8" />
              </div>
            </div>
            <h3 class="text-xl font-semibold text-[#006D77] text-center mb-4">
              Expert Mentorship
            </h3>
            <p class="text-gray-600 text-center">
              Learn directly from industry professionals with years of experience who provide personalized guidance, code reviews, and career advice.
            </p>
          </div>
        </div>
      </div>
    </section>

    <!-- Programs Section -->
    <section class="py-12">
      <div class="container mx-auto px-4">
        <h2 class="text-4xl font-bold text-center mb-3">Unlock New Skills, from Starter to Master!</h2>
        <p class="text-center text-gray-600 text-xl mb-14">
          Choose from our specialized programs taught by industry experts and gain the skills employers are looking for.
        </p>

        <!-- Program Header and Content -->
        <div>
          <div>
            <!-- Header with Yellow Circle -->
            <div class="relative flex mb-4 w-full">
              <div class="absolute inset-0 flex items-start -translate-y-5">
                <div class="w-[70px] h-[70px] bg-[#FFC107] rounded-full shadow-sm opacity-90"></div>
              </div>
              <h3 class="font-bold text-4xl relative z-10 text-gray-800 px-4">Pick Your Program!</h3>
            </div>

            <!-- Program Types and Cards Container -->
            <div class="grid grid-cols-1 md:grid-cols-12 gap-6">
              <!-- Program Types Column -->
              <div class="md:col-span-3">
                <div class="sticky top-5 md:min-w-[260px] w-full">
                  <div class="flex flex-col gap-3">
                    <button
                      v-for="type in programTypes"
                      :key="type.id"
                      class="flex items-center gap-2 sm:gap-3 w-full justify-start px-4 sm:px-6 py-2 sm:py-3 bg-[#FFF9F9] border rounded-lg transition-all duration-200 hover:bg-gray-50"
                      :class="{ 'border-[#F2720C] shadow-sm': selectedType === type.id }"
                      @click="selectedType = type.id"
                    >
                      <div class="flex items-center justify-center w-6 h-6 flex-shrink-0">
                        <img
                          :src="type.icon"
                          :alt="type.name"
                          class="w-5 h-5 object-contain"
                        />
                      </div>
                      <span class="text-gray-700 text-sm sm:text-base">{{ type.name }}</span>
                    </button>
                  </div>
                </div>
              </div>

              <!-- Program Cards Column -->
              <div class="md:col-span-9">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <template v-if="filteredPrograms.length > 0">
                    <div v-for="program in filteredPrograms" :key="program.id" class="mb-4">
                      <ProgramCard
                        :program="program"
                        :initialMentorIndex="0"
                      />
                    </div>
                  </template>
                  <template v-else>
                    <div class="col-span-2 text-center py-12">
                      <div class="inline-flex items-center bg-blue-100 text-blue-700 px-4 py-2 rounded-lg">
                        No mentors available for this program type at the moment.
                      </div>
                    </div>
                  </template>
                </div>

                <!-- See More Button -->
                <div v-if="totalPrograms > 2" class="mt-8 pl-3">
                  <router-link
                    to="/program"
                    class="inline-flex items-center px-6 py-3 bg-[#008080] text-white rounded-lg hover:bg-[#006666] transition-colors duration-200"
                  >
                    See More
                    <svg class="w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14 5l7 7m0 0l-7 7m7-7H3"></path>
                    </svg>
                  </router-link>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Career Stages Section -->
    <section class="py-16 bg-white">
      <div class="container mx-auto px-4">
        <h2 class="text-4xl font-bold text-center mb-12">This Bootcamp is Designed for All Stages of Your Career.</h2>

        <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
          <!-- Students -->
          <div class="bg-white rounded-lg shadow-md overflow-hidden transition-transform duration-300 hover:-translate-y-1">
            <div class="h-48 overflow-hidden">
              <img
                src="/join.png"
                alt="Students"
                class="w-full h-full object-cover"
              >
            </div>
            <div class="p-6">
              <h4 class="text-center font-bold text-xl mb-4">STUDENTS</h4>
              <p class="text-center text-gray-600">
                Prepare your career early with skills that are relevant to today's working world.
              </p>
            </div>
          </div>

          <!-- Fresh Graduate -->
          <div class="bg-white rounded-lg shadow-md overflow-hidden transition-transform duration-300 hover:-translate-y-1">
            <div class="h-48 overflow-hidden">
              <img
                src="/join.png"
                alt="Fresh Graduate"
                class="w-full h-full object-cover"
              >
            </div>
            <div class="p-6">
              <h4 class="text-center font-bold text-xl mb-4">FRESH GRADUATE</h4>
              <p class="text-center text-gray-600">
                Achieve your dream career with intensive guidance complete with job placement
              </p>
            </div>
          </div>

          <!-- Professional Worker -->
          <div class="bg-white rounded-lg shadow-md overflow-hidden transition-transform duration-300 hover:-translate-y-1">
            <div class="h-48 overflow-hidden">
              <img
                src="/join.png"
                alt="Professional Worker"
                class="w-full h-full object-cover"
              >
            </div>
            <div class="p-6">
              <h4 class="text-center font-bold text-xl mb-4">PROFESSIONAL WORKER</h4>
              <p class="text-center text-gray-600">
                Upgrade your career through learning programs tailored to industry needs.
              </p>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Testimonial Section -->
    <section class="py-12 bg-[#FF8C00]">
      <div class="container mx-auto px-4">
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 items-center">
          <!-- Left Column -->
          <div>
            <p class="text-4xl text-white mb-4">TESTIMONIAL</p>
            <h2 class="text-white font-bold text-4xl mb-8">What They Say?</h2>
            <p class="text-white text-lg mb-8">
              FlowCamp has received over 10,000 positive reviews from our graduates who have successfully transitioned into tech careers.
            </p>
            <div>
              <router-link to="/testimonials" class="bg-white text-gray-800 px-6 py-3 rounded-full inline-flex items-center gap-2 hover:bg-gray-100 transition-colors">
                <span class="font-medium">Write your assessment</span>
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="w-5 h-5">
                  <line x1="5" y1="12" x2="19" y2="12"></line>
                  <polyline points="12 5 19 12 12 19"></polyline>
                </svg>
              </router-link>
            </div>
          </div>

          <!-- Right Column -->
          <div class="relative">
            <!-- Student Image -->
            <div class="relative">
              <img
                src="/testi-smile.png"
                alt="Student"
                class="w-[300px] h-[400px] rounded-2xl object-cover"
              >
            </div>

            <!-- Testimonial Card -->
            <div class="absolute bottom-[-20px] right-[-20px] bg-white p-8 rounded-2xl shadow-lg max-w-md">
              <p class="mb-6 text-gray-700 text-lg leading-relaxed">
                "The Back-End Development Bootcamp was a game-changer for me! The mentors were incredibly supportive,
                and I learned Nest JS and Golang from scratch."
              </p>

              <div class="flex justify-between items-end">
                <div>
                  <h6 class="font-bold text-xl text-gray-800 mb-1">Elly Melly</h6>
                  <span class="text-gray-500">Student</span>
                </div>
                <div class="flex flex-col items-end gap-2">
                  <div class="flex gap-1">
                    <img v-for="i in 5" :key="i" src="/star-testi.png" alt="Star Rating" class="w-5 h-5" />
                  </div>
                  <div class="text-gray-500 text-sm">
                    10 Review at yelp
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Bootcamp Courses Section -->
    <section class="py-16 bg-gray-50">
      <div class="container mx-auto px-4">
        <div class="mb-12">
          <h2 class="text-4xl font-bold mb-4">Start learning from the beginning, accompanied until work!</h2>
          <p class="text-gray-600 text-xl">Let's join our famous class, the knowledge provided will definitely be useful for you.</p>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
          <!-- Course Card 1 -->
          <CourseCard
            :course="{
              id: 1,
              title: 'Crafting Stunning Websites: How the Back-End Bootcamp Transformed My Career',
              image: '/header.jpg',
              badge: 'Bootcamp Front End Batch 8',
              duration: '01 hr 12 mins',
              instructors: 'Thomas Anderson and Emy Thomas'
            }"
          />

          <!-- Course Card 2 -->
          <CourseCard
            :course="{
              id: 2,
              title: 'Breaking Into Tech: How the Back-End Bootcamp Helped Me',
              image: '/header.jpg',
              badge: 'Bootcamp Back End Batch 7',
              duration: '01 hr 03 mins',
              instructors: 'William Anderson and Sam Nuren'
            }"
          />

          <!-- Course Card 3 -->
          <CourseCard
            :course="{
              id: 3,
              title: 'Designing the Future: How the Back-End Bootcamp Launched My Career',
              image: '/header.jpg',
              badge: 'Bootcamp Full Stack Batch 6',
              duration: '01 hr 02 mins',
              instructors: 'Mickey Mimi and Mini Mimi'
            }"
          />
        </div>
      </div>
    </section>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue';
import { programs, programTypes } from '@/data/programs';
import ProgramCard from '@/components/ProgramCard.vue';
import CourseCard from '@/components/CourseCard.vue';
import HeroSection from '@/components/HeroSection.vue';
import StudentImageSection from '@/components/StudentImageSection.vue';

const bootcampHero = '/bootcampHero.png';
const studentImage = '/success.png';

const selectedType = ref('backend');

const filteredPrograms = computed(() => {
  const filtered = programs.filter(program =>
    program.type === selectedType.value
  );
  return filtered.slice(0, 6);
});

const totalPrograms = computed(() => {
  return programs.filter(program => program.type === selectedType.value).length;
});

if (window.location.search.includes('program=')) {
  const urlParams = new URLSearchParams(window.location.search);
  selectedType.value = urlParams.get('program');
}

window.scrollTo({
  top: 0,
  behavior: 'smooth'
});
</script>


