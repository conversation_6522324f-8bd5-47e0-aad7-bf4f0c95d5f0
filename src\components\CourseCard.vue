<template>
  <div class="bg-white h-full rounded-xl overflow-hidden border border-black/10 shadow-md transition-all duration-200 hover:-translate-y-1 hover:shadow-lg group">
    <div class="relative">
      <!-- Image with duration overlay -->
      <img
        :src="course.image"
        :alt="course.title"
        class="w-full h-48 object-cover md:h-44"
      >
      <div class="absolute top-0 right-0 m-3">
        <div class="inline-flex items-center px-3 py-1 text-xs bg-white/90 backdrop-blur-sm shadow-sm rounded-full">
          <img
            src="/time.png"
            alt="Duration icon"
            class="w-3 h-3 mr-1 object-contain"
          />
          <span class="text-gray-900">{{ course.duration }}</span>
        </div>
      </div>
    </div>
    <div class="p-5">
      <div class="text-sm font-medium text-emerald-600 mb-3">
        {{ course.badge }}
      </div>
      <h4 class="font-bold text-2xl leading-tight mb-2 text-gray-900">{{ course.title }}</h4>
      <p class="text-gray-500 text-sm">with {{ course.instructors }}</p>
    </div>
  </div>
</template>

<script setup>
defineProps({
  course: {
    type: Object,
    required: true,
    validator: (prop) => {
      return [
        'id',
        'title',
        'image',
        'badge',
        'duration',
        'instructors'
      ].every(key => key in prop)
    }
  }
});
</script>