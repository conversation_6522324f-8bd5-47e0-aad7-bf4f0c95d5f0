<template>
  <StudentLayoutWrapper contentClass="bg-gray-50">
    <div class="mb-6">
      <h1 class="text-2xl font-bold text-gray-800">Settings</h1>
      <p class="text-gray-600 mt-1">Manage your account and preferences</p>
    </div>

    <!-- Settings Tabs -->
    <div class="bg-white rounded-lg shadow-sm overflow-hidden mb-8">
      <div class="border-b border-gray-200">
        <nav class="flex -mb-px">
          <button
            v-for="tab in tabs"
            :key="tab.id"
            @click="activeTab = tab.id"
            class="py-4 px-6 text-center font-medium text-sm relative group"
          >
            <div class="flex items-center justify-center">
              <span class="mr-2" v-html="getTabIcon(tab.id)"></span>
              <span :class="activeTab === tab.id ? 'text-orange-600' : 'text-gray-500 group-hover:text-gray-700'">
                {{ tab.name }}
              </span>
            </div>

            <!-- Active tab indicator - perfectly aligned with text -->
            <div
              v-if="activeTab === tab.id"
              class="absolute bottom-0 left-0 right-0 h-0.5 bg-orange-500 transition-all duration-300"
              :style="{ width: '100%', bottom: '-1px' }"
            ></div>

            <!-- Hover indicator for inactive tabs -->
            <div
              v-else
              class="absolute bottom-0 left-0 right-0 h-0.5 bg-gray-300 scale-x-0 group-hover:scale-x-100 transition-transform duration-300"
              :style="{ width: '100%', bottom: '-1px' }"
            ></div>
          </button>
        </nav>
      </div>

      <!-- Profile Tab -->
      <div v-if="activeTab === 'profile'" class="p-6">
        <form @submit.prevent="saveProfile" class="space-y-6">
          <!-- Profile Picture -->
          <div class="flex items-center space-x-6">
            <div class="relative group">
              <!-- Profile Image -->
              <img
                :src="profile.avatar || '/prof.png'"
                alt="Profile Picture"
                class="h-24 w-24 rounded-full object-cover border-2 border-gray-200"
                @error="handleProfileImageError"
              />

              <!-- Hover Overlay - Full Image Clickable Area -->
              <div
                @click="triggerFileInput"
                class="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-20 rounded-full flex items-center justify-center transition-all duration-200 cursor-pointer"
              >
                <div class="text-white opacity-0 group-hover:opacity-100 transition-opacity duration-200">
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 9a2 2 0 012-2h.93a2 2 0 001.664-.89l.812-1.22A2 2 0 0110.07 4h3.86a2 2 0 011.664.89l.812 1.22A2 2 0 0018.07 7H19a2 2 0 012 2v9a2 2 0 01-2 2H5a2 2 0 01-2-2V9z" />
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 13a3 3 0 11-6 0 3 3 0 016 0z" />
                  </svg>
                </div>
              </div>
            </div>

            <div>
              <h3 class="text-lg font-medium text-gray-900">Profile Picture</h3>
              <p class="text-sm text-gray-500">JPG, GIF or PNG. 1MB max.</p>

              <!-- Hidden File Input -->
              <input
                type="file"
                ref="fileInput"
                @change="handleFileChange"
                accept="image/jpeg, image/png, image/gif"
                class="hidden"
              />
            </div>
          </div>

          <!-- Name and Email -->
          <div class="grid grid-cols-1 gap-6 sm:grid-cols-2">
            <div>
              <label for="fullName" class="block text-sm font-medium text-gray-700">Full Name</label>
              <input
                type="text"
                id="fullName"
                v-model="profile.fullName"
                class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-orange-500 focus:border-orange-500 sm:text-sm"
                required
              />
            </div>
            <div>
              <label for="email" class="block text-sm font-medium text-gray-700">Email Address</label>
              <input
                type="email"
                id="email"
                v-model="profile.email"
                class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-orange-500 focus:border-orange-500 sm:text-sm"
                required
              />
            </div>
          </div>

          <!-- Phone and Location -->
          <div class="grid grid-cols-1 gap-6 sm:grid-cols-2">
            <div>
              <label for="phone" class="block text-sm font-medium text-gray-700">Phone Number</label>
              <input
                type="tel"
                id="phone"
                v-model="profile.phone"
                class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-orange-500 focus:border-orange-500 sm:text-sm"
              />
            </div>
            <div>
              <label for="location" class="block text-sm font-medium text-gray-700">Location</label>
              <input
                type="text"
                id="location"
                v-model="profile.location"
                class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-orange-500 focus:border-orange-500 sm:text-sm"
              />
            </div>
          </div>

          <!-- Bio -->
          <div>
            <label for="bio" class="block text-sm font-medium text-gray-700">Bio</label>
            <textarea
              id="bio"
              v-model="profile.bio"
              rows="3"
              class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-orange-500 focus:border-orange-500 sm:text-sm"
              placeholder="Tell us a little about yourself"
            ></textarea>
          </div>

          <!-- Save Button -->
          <div class="flex justify-end">
            <button
              type="submit"
              class="px-4 py-2 bg-orange-600 text-white rounded-md text-sm font-medium hover:bg-orange-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500 transition-colors"
              :disabled="isSubmitting"
            >
              <span v-if="isSubmitting" class="flex items-center">
                <svg class="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                  <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                Saving...
              </span>
              <span v-else>Save Changes</span>
            </button>
          </div>
        </form>
      </div>

      <!-- Account Tab -->
      <div v-if="activeTab === 'account'" class="p-6">
        <!-- Change Password -->
        <div class="mb-8">
          <h3 class="text-lg font-medium text-gray-900 mb-4">Change Password</h3>
          <form @submit.prevent="changePassword" class="space-y-4">
            <div>
              <label for="currentPassword" class="block text-sm font-medium text-gray-700">Current Password</label>
              <input
                type="password"
                id="currentPassword"
                v-model="passwordForm.currentPassword"
                class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-orange-500 focus:border-orange-500 sm:text-sm"
                required
              />
            </div>
            <div>
              <label for="newPassword" class="block text-sm font-medium text-gray-700">New Password</label>
              <input
                type="password"
                id="newPassword"
                v-model="passwordForm.newPassword"
                class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-orange-500 focus:border-orange-500 sm:text-sm"
                required
              />
            </div>
            <div>
              <label for="confirmPassword" class="block text-sm font-medium text-gray-700">Confirm New Password</label>
              <input
                type="password"
                id="confirmPassword"
                v-model="passwordForm.confirmPassword"
                class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-orange-500 focus:border-orange-500 sm:text-sm"
                required
              />
              <p v-if="passwordError" class="mt-1 text-sm text-red-600">{{ passwordError }}</p>
            </div>
            <div class="flex justify-end">
              <button
                type="submit"
                class="px-4 py-2 bg-orange-600 text-white rounded-md text-sm font-medium hover:bg-orange-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500 transition-colors"
                :disabled="isSubmitting"
              >
                Update Password
              </button>
            </div>
          </form>
        </div>

        <!-- Danger Zone -->
        <div>
          <h3 class="text-lg font-medium text-gray-900 mb-4">Danger Zone</h3>
          <div class="bg-red-50 border border-red-200 rounded-md p-4">
            <div class="flex">
              <div class="flex-shrink-0">
                <svg class="h-5 w-5 text-red-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                  <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
                </svg>
              </div>
              <div class="ml-3">
                <h3 class="text-sm font-medium text-red-800">Delete Account</h3>
                <div class="mt-2 text-sm text-red-700">
                  <p>Once you delete your account, there is no going back. Please be certain.</p>
                </div>
                <div class="mt-4">
                  <button
                    type="button"
                    @click="confirmDeleteAccount"
                    class="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-red-700 bg-red-100 hover:bg-red-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
                  >
                    Delete Account
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Notifications Tab -->
      <div v-if="activeTab === 'notifications'" class="p-6">
        <div class="flex justify-between items-center mb-4">
          <h3 class="text-lg font-medium text-gray-900">Notification Preferences</h3>
          <div class="flex space-x-3">
            <button
              @click="toggleAllNotifications(true)"
              class="text-xs px-2 py-1 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 transition-colors"
              type="button"
            >
              Enable All
            </button>
            <button
              @click="toggleAllNotifications(false)"
              class="text-xs px-2 py-1 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 transition-colors"
              type="button"
            >
              Disable All
            </button>
          </div>
        </div>

        <div class="space-y-4 mt-4">
          <div v-for="(notification, index) in notifications" :key="index" class="flex items-start p-3 rounded-lg hover:bg-gray-50 transition-colors">
            <div class="flex items-center h-5">
              <input
                :id="`notification-${index}`"
                v-model="notification.enabled"
                type="checkbox"
                class="focus:ring-orange-500 h-4 w-4 text-orange-600 border-gray-300 rounded"
              />
            </div>
            <div class="ml-3 text-sm">
              <label :for="`notification-${index}`" class="font-medium text-gray-700">{{ notification.title }}</label>
              <p class="text-gray-500">{{ notification.description }}</p>
            </div>
          </div>
        </div>

        <div class="mt-6 flex justify-end">
          <button
            @click="saveNotificationPreferences"
            class="px-4 py-2 bg-orange-600 text-white rounded-md text-sm font-medium hover:bg-orange-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500 transition-colors"
            :disabled="isSubmitting"
          >
            <span v-if="isSubmitting" class="flex items-center">
              <svg class="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
              Saving...
            </span>
            <span v-else>Save Preferences</span>
          </button>
        </div>
      </div>
    </div>

    <!-- Delete Account Confirmation Modal -->
    <div v-if="showDeleteModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div class="bg-white rounded-lg shadow-xl max-w-md w-full">
        <div class="p-6">
          <div class="flex items-center justify-center w-12 h-12 mx-auto bg-red-100 rounded-full mb-4">
            <svg class="h-6 w-6 text-red-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
            </svg>
          </div>
          <h3 class="text-lg font-medium text-center text-gray-900 mb-2">Delete Account</h3>
          <p class="text-sm text-gray-500 text-center mb-4">
            Are you sure you want to delete your account? All of your data will be permanently removed. This action cannot be undone.
          </p>
          <div class="mt-4 flex justify-center space-x-4">
            <button
              @click="showDeleteModal = false"
              class="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500"
            >
              Cancel
            </button>
            <button
              @click="deleteAccount"
              class="px-4 py-2 bg-red-600 text-white rounded-md text-sm font-medium hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 disabled:bg-red-400 disabled:cursor-not-allowed"
              :disabled="isSubmitting"
            >
              <span v-if="isSubmitting" class="flex items-center">
                <svg class="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                  <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                Deleting...
              </span>
              <span v-else>Delete</span>
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- Image Preview Modal -->
    <div v-if="showImagePreview" class="fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50 p-4">
      <div class="bg-white rounded-lg shadow-xl max-w-md w-full overflow-hidden">
        <div class="p-4 border-b border-gray-200 flex justify-between items-center">
          <h3 class="text-lg font-medium text-gray-900">Preview Profile Picture</h3>
          <button
            @click="cancelImageUpload"
            class="text-gray-400 hover:text-gray-500 focus:outline-none"
          >
            <svg class="h-6 w-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        <div class="p-6 flex flex-col items-center">
          <!-- Image Preview -->
          <div class="relative w-48 h-48 mb-4">
            <img
              :src="imagePreviewUrl"
              alt="Profile Picture Preview"
              class="w-48 h-48 rounded-full object-cover border-2 border-gray-200"
            />
          </div>

          <!-- File Info -->
          <div v-if="selectedFile" class="text-sm text-gray-500 mb-4 text-center">
            <p>{{ selectedFile.name }}</p>
            <p>{{ formatFileSize(selectedFile.size) }}</p>
          </div>

          <!-- Error Message -->
          <p v-if="imageError" class="text-red-500 text-sm mb-4">{{ imageError }}</p>

          <!-- Action Buttons -->
          <div class="flex space-x-3">
            <button
              @click="cancelImageUpload"
              class="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500"
            >
              Cancel
            </button>
            <button
              @click="confirmImageUpload"
              class="px-4 py-2 bg-orange-600 text-white rounded-md text-sm font-medium hover:bg-orange-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500"
              :disabled="!!imageError"
            >
              Set as Profile Picture
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- Success Toast -->
    <div
      v-if="showSuccessToast"
      class="fixed bottom-4 right-4 bg-green-50 border-l-4 border-green-400 p-4 rounded shadow-md transition-all duration-500 transform"
      :class="{ 'translate-y-0 opacity-100': showSuccessToast, 'translate-y-10 opacity-0': !showSuccessToast }"
    >
      <div class="flex">
        <div class="flex-shrink-0">
          <svg class="h-5 w-5 text-green-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
          </svg>
        </div>
        <div class="ml-3">
          <p class="text-sm font-medium text-green-800">{{ successMessage }}</p>
        </div>
        <div class="ml-auto pl-3">
          <div class="-mx-1.5 -my-1.5">
            <button
              @click="showSuccessToast = false"
              class="inline-flex bg-green-50 rounded-md p-1.5 text-green-500 hover:bg-green-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500"
            >
              <svg class="h-4 w-4" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd" />
              </svg>
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- Error Toast -->
    <div
      v-if="showErrorToast"
      class="fixed bottom-4 right-4 bg-red-50 border-l-4 border-red-400 p-4 rounded shadow-md transition-all duration-500 transform"
      :class="{ 'translate-y-0 opacity-100': showErrorToast, 'translate-y-10 opacity-0': !showErrorToast }"
    >
      <div class="flex">
        <div class="flex-shrink-0">
          <svg class="h-5 w-5 text-red-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
          </svg>
        </div>
        <div class="ml-3">
          <p class="text-sm font-medium text-red-800">{{ errorMessage }}</p>
        </div>
        <div class="ml-auto pl-3">
          <div class="-mx-1.5 -my-1.5">
            <button
              @click="showErrorToast = false"
              class="inline-flex bg-red-50 rounded-md p-1.5 text-red-500 hover:bg-red-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
            >
              <svg class="h-4 w-4" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd" />
              </svg>
            </button>
          </div>
        </div>
      </div>
    </div>
  </StudentLayoutWrapper>
</template>

<script setup>
import { ref, onMounted, watch } from 'vue';
import StudentLayoutWrapper from '@/components/@student/StudentLayoutWrapper.vue';

// Tab state
const activeTab = ref('profile');
const tabs = [
  {
    id: 'profile',
    name: 'Profile'
  },
  {
    id: 'account',
    name: 'Account'
  },
  {
    id: 'notifications',
    name: 'Notifications'
  }
];

// Watch for tab changes and save to localStorage
watch(activeTab, (newTab) => {
  localStorage.setItem('settings_active_tab', newTab);
});

// Form states
const isSubmitting = ref(false);
const showSuccessToast = ref(false);
const successMessage = ref('');
const showDeleteModal = ref(false);
const showErrorToast = ref(false);
const errorMessage = ref('');

// Profile picture states
const fileInput = ref(null);
const selectedFile = ref(null);
const imagePreviewUrl = ref('');
const showImagePreview = ref(false);
const imageError = ref('');

// Profile form
const profile = ref({
  fullName: '',
  email: '',
  phone: '',
  location: '',
  bio: '',
  avatar: '/prof.png'
});

// Password form
const passwordForm = ref({
  currentPassword: '',
  newPassword: '',
  confirmPassword: ''
});
const passwordError = ref('');

// Notification preferences
const notifications = ref([
  {
    title: 'Email Notifications',
    description: 'Receive email notifications about your account, classes, and updates.',
    enabled: true
  },
  {
    title: 'Class Reminders',
    description: 'Get reminders about upcoming classes and assignments.',
    enabled: true
  },
  {
    title: 'New Content Alerts',
    description: 'Be notified when new content is added to your enrolled classes.',
    enabled: true
  },
  {
    title: 'Marketing Communications',
    description: 'Receive updates about new courses, promotions, and events.',
    enabled: false
  }
]);

// Profile Picture Functions
// Trigger file input click
const triggerFileInput = () => {
  if (fileInput.value) {
    fileInput.value.click();
  }
};

// Handle file selection
const handleFileChange = (event) => {
  const file = event.target.files[0];
  if (!file) return;

  // Reset error state
  imageError.value = '';

  // Validate file type
  const validTypes = ['image/jpeg', 'image/png', 'image/gif'];
  if (!validTypes.includes(file.type)) {
    imageError.value = 'Please select a valid image file (JPG, PNG, or GIF)';
    showErrorToast.value = true;
    errorMessage.value = imageError.value;

    // Reset file input
    event.target.value = '';

    // Hide error toast after 3 seconds
    setTimeout(() => {
      showErrorToast.value = false;
    }, 3000);

    return;
  }

  // Validate file size (1MB = 1048576 bytes)
  if (file.size > 1048576) {
    imageError.value = 'Image size should not exceed 1MB';
    showErrorToast.value = true;
    errorMessage.value = imageError.value;

    // Reset file input
    event.target.value = '';

    // Hide error toast after 3 seconds
    setTimeout(() => {
      showErrorToast.value = false;
    }, 3000);

    return;
  }

  // Store the selected file
  selectedFile.value = file;

  // Create a preview URL
  const reader = new FileReader();
  reader.onload = (e) => {
    imagePreviewUrl.value = e.target.result;
    showImagePreview.value = true;
  };
  reader.readAsDataURL(file);
};

// Format file size for display
const formatFileSize = (bytes) => {
  if (bytes < 1024) return bytes + ' bytes';
  else if (bytes < 1048576) return (bytes / 1024).toFixed(1) + ' KB';
  else return (bytes / 1048576).toFixed(1) + ' MB';
};

// Cancel image upload
const cancelImageUpload = () => {
  // Reset file input
  if (fileInput.value) {
    fileInput.value.value = '';
  }

  // Reset state
  selectedFile.value = null;
  imagePreviewUrl.value = '';
  showImagePreview.value = false;
  imageError.value = '';
};

// Confirm image upload
const confirmImageUpload = () => {
  if (imageError.value) return;

  // Update profile avatar with the new image
  profile.value.avatar = imagePreviewUrl.value;

  // Save to localStorage
  localStorage.setItem('user_profile', JSON.stringify(profile.value));

  // Show success message
  successMessage.value = 'Profile picture updated successfully!';
  showSuccessToast.value = true;

  // Hide toast after 3 seconds
  setTimeout(() => {
    showSuccessToast.value = false;
  }, 3000);

  // Close the preview modal
  showImagePreview.value = false;

  // Reset state
  selectedFile.value = null;
  if (fileInput.value) {
    fileInput.value.value = '';
  }
};

// Handle profile image error
const handleProfileImageError = (event) => {
  // If the profile image fails to load, use the default image
  event.target.src = '/prof.png';
};

// Tab icons as inline SVG components
const getTabIcon = (tabId) => {
  switch (tabId) {
    case 'profile':
      return `
        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
        </svg>
      `;
    case 'account':
      return `
        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
        </svg>
      `;
    case 'notifications':
      return `
        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 17h5l-1.405-1.405A2.032 2.032 0 0118 14.158V11a6.002 6.002 0 00-4-5.659V5a2 2 0 10-4 0v.341C7.67 6.165 6 8.388 6 11v3.159c0 .538-.214 1.055-.595 1.436L4 17h5m6 0v1a3 3 0 11-6 0v-1m6 0H9" />
        </svg>
      `;
    default:
      return '';
  }
};

// Load user profile from localStorage
const loadUserProfile = () => {
  try {
    const storedProfile = localStorage.getItem('user_profile');
    if (storedProfile) {
      profile.value = { ...profile.value, ...JSON.parse(storedProfile) };
    }
  } catch (error) {
    console.error('Error loading profile:', error);
  }
};

// Save profile
const saveProfile = () => {
  isSubmitting.value = true;

  // Validate form
  if (!profile.value.fullName || !profile.value.email) {
    successMessage.value = 'Please fill in all required fields';
    showSuccessToast.value = true;
    isSubmitting.value = false;

    // Hide toast after 3 seconds
    setTimeout(() => {
      showSuccessToast.value = false;
    }, 3000);
    return;
  }

  // Validate email format
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  if (!emailRegex.test(profile.value.email)) {
    successMessage.value = 'Please enter a valid email address';
    showSuccessToast.value = true;
    isSubmitting.value = false;

    // Hide toast after 3 seconds
    setTimeout(() => {
      showSuccessToast.value = false;
    }, 3000);
    return;
  }

  // Simulate API call
  setTimeout(() => {
    // Save to localStorage
    localStorage.setItem('user_profile', JSON.stringify(profile.value));

    // Show success message
    successMessage.value = 'Profile updated successfully!';
    showSuccessToast.value = true;
    isSubmitting.value = false;

    // Hide toast after 3 seconds
    setTimeout(() => {
      showSuccessToast.value = false;
    }, 3000);
  }, 1000);
};

// Change password
const changePassword = () => {
  // Reset error
  passwordError.value = '';

  // Validate current password
  if (!passwordForm.value.currentPassword) {
    passwordError.value = 'Current password is required';
    return;
  }

  // Validate new password
  if (!passwordForm.value.newPassword) {
    passwordError.value = 'New password is required';
    return;
  }

  // Validate password length
  if (passwordForm.value.newPassword.length < 8) {
    passwordError.value = 'Password must be at least 8 characters';
    return;
  }

  // Validate password complexity
  const hasUpperCase = /[A-Z]/.test(passwordForm.value.newPassword);
  const hasLowerCase = /[a-z]/.test(passwordForm.value.newPassword);
  const hasNumbers = /\d/.test(passwordForm.value.newPassword);

  if (!(hasUpperCase && hasLowerCase && hasNumbers)) {
    passwordError.value = 'Password must contain at least one uppercase letter, one lowercase letter, and one number';
    return;
  }

  // Validate password confirmation
  if (passwordForm.value.newPassword !== passwordForm.value.confirmPassword) {
    passwordError.value = 'Passwords do not match';
    return;
  }

  // All validations passed
  isSubmitting.value = true;

  // Simulate API call
  setTimeout(() => {
    // Save to localStorage (in a real app, this would be an API call)
    localStorage.setItem('password_changed', 'true');

    // Reset form
    passwordForm.value = {
      currentPassword: '',
      newPassword: '',
      confirmPassword: ''
    };

    // Show success message
    successMessage.value = 'Password changed successfully!';
    showSuccessToast.value = true;
    isSubmitting.value = false;

    // Hide toast after 3 seconds
    setTimeout(() => {
      showSuccessToast.value = false;
    }, 3000);
  }, 1000);
};

// Save notification preferences
const saveNotificationPreferences = () => {
  isSubmitting.value = true;

  // Simulate API call
  setTimeout(() => {
    // Save to localStorage
    localStorage.setItem('notification_preferences', JSON.stringify(notifications.value));

    // Update any related components or state
    // In a real app, this might involve updating a global state or making API calls

    // Show success message
    successMessage.value = 'Notification preferences saved!';
    showSuccessToast.value = true;
    isSubmitting.value = false;

    // Hide toast after 3 seconds
    setTimeout(() => {
      showSuccessToast.value = false;
    }, 3000);
  }, 1000);
};

// Toggle all notifications
const toggleAllNotifications = (enabled) => {
  notifications.value.forEach(notification => {
    notification.enabled = enabled;
  });
};

// Confirm delete account
const confirmDeleteAccount = () => {
  showDeleteModal.value = true;
};

// Delete account
const deleteAccount = () => {
  isSubmitting.value = true;

  // Simulate API call
  setTimeout(() => {
    // In a real app, this would call an API to delete the account

    // Clear all user data from localStorage
    localStorage.removeItem('user_profile');
    localStorage.removeItem('notification_preferences');
    localStorage.removeItem('password_changed');
    localStorage.removeItem('flowcamp-notifications');
    localStorage.removeItem('flowcamp-cleared-notifications');
    localStorage.removeItem('flowcamp_sidebar_open');

    // Clear any other app-specific data
    const storageKeys = Object.keys(localStorage);
    storageKeys.forEach(key => {
      if (key.startsWith('flowcamp-')) {
        localStorage.removeItem(key);
      }
    });

    // Redirect to login page
    window.location.href = '/login';
  }, 1500);
};

// Lifecycle hooks
onMounted(() => {
  // Load user profile
  loadUserProfile();

  // Load notification preferences
  try {
    const storedPreferences = localStorage.getItem('notification_preferences');
    if (storedPreferences) {
      notifications.value = JSON.parse(storedPreferences);
    }
  } catch (error) {
    console.error('Error loading notification preferences:', error);
  }

  // Check if there's a saved active tab
  const savedTab = localStorage.getItem('settings_active_tab');
  if (savedTab && tabs.some(tab => tab.id === savedTab)) {
    activeTab.value = savedTab;
  }

  // Add event listener for ESC key to close image preview
  const handleEscKey = (event) => {
    if (event.key === 'Escape' && showImagePreview.value) {
      cancelImageUpload();
    }
  };

  window.addEventListener('keydown', handleEscKey);

  // Clean up event listener on unmount
  return () => {
    window.removeEventListener('keydown', handleEscKey);
  };
});
</script>
