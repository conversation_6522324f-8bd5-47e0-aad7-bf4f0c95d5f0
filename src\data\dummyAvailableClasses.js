/**
 * Dummy Available Classes Data
 *
 * This file contains manually defined data for available classes that students can join.
 * These classes are displayed in the Classes Camp page.
 *
 * Data Structure:
 * - id: Unique identifier for the class (should be a number)
 * - title: Descriptive title of the class
 * - rating: Class rating (number between 1-5, can include decimal)
 * - studentsEnrolled: Number of students enrolled in the class
 * - modules: Number of modules in the class
 * - category: Category of the class (should be one of: 'frontend', 'backend', 'mobile')
 * - description: Detailed description of what the class covers
 * - postedDate: Date when the class was posted (format: YYYY-MM-DD)
 * - postedTime: Time when the class was posted (format: HH:MM AM/PM)
 * - imageUrl: Path to the class image (should be a string pointing to an image in the public directory)
 * - status: Status of the class (should be "available" for classes that can be joined)
 */

// Define categories
const CATEGORIES = {
  FRONTEND: 'frontend',
  BACKEND: 'backend',
  MOBILE: 'mobile'
};

// Frontend classes
const frontendClasses = [
  {
    id: 101,
    title: "Advanced JavaScript: Modern ES6+ Features",
    rating: 4.9,
    studentsEnrolled: 142,
    modules: 7,
    category: CATEGORIES.FRONTEND,
    description: "Take your JavaScript skills to the next level with this comprehensive course on modern ES6+ features. Learn about arrow functions, destructuring, async/await, generators, and more. By the end of this course, you'll be writing cleaner, more efficient JavaScript code and be prepared for modern frontend and backend development.",
    postedDate: "2024-11-15",
    postedTime: "09:30 AM",
    imageUrl: "/class1.png",
    status: "available",
    // Detailed category description
    categoryDescription: "frontend development, including modern JavaScript frameworks, responsive design, and user interface implementation. You'll learn how to create engaging, interactive web applications with clean, maintainable code.",
    // Targets and goals specific to this class
    targetsAndGoals: {
      description: "This class is designed to help you master the fundamentals and advanced concepts of frontend development, with a focus on modern JavaScript. You'll learn through hands-on projects and practical examples that will prepare you for real-world applications.",
      goals: [
        "Master modern JavaScript ES6+ features and syntax",
        "Build interactive web applications with clean, maintainable code",
        "Understand asynchronous programming patterns and best practices",
        "Learn industry-standard development tools and workflows",
        "Develop problem-solving skills through real-world coding challenges",
        "Create a portfolio of JavaScript projects to showcase your skills"
      ]
    },
    // Learning tools required for this class
    learningTools: {
      deviceSpecs: {
        processor: "Intel Dual Core (Core i3 and above recommended)"
      },
      tools: [
        "Visual Studio Code",
        "Node.js",
        "Chrome DevTools",
        "Git & GitHub"
      ],
      additionalRequirements: []
    },
    // Module data for this class
    moduleData: [
      {
        title: "ES6+ Syntax and Features",
        description: "Learn modern JavaScript syntax including let/const, arrow functions, template literals, and destructuring assignments."
      },
      {
        title: "Asynchronous JavaScript",
        description: "Master promises, async/await, and callback patterns for handling asynchronous operations effectively."
      },
      {
        title: "Advanced Functions and Closures",
        description: "Explore higher-order functions, closures, and functional programming concepts in JavaScript."
      },
      {
        title: "Object-Oriented Programming in JS",
        description: "Understand classes, inheritance, prototypes, and modern OOP patterns in JavaScript."
      },
      {
        title: "Modules and Build Tools",
        description: "Learn ES6 modules, import/export syntax, and modern build tools like Webpack and Vite."
      },
      {
        title: "Error Handling and Debugging",
        description: "Master error handling techniques, debugging strategies, and testing methodologies."
      },
      {
        title: "Performance Optimization",
        description: "Optimize JavaScript code for better performance, memory management, and user experience."
      }
    ]
  },
  {
    id: 102,
    title: "Web Accessibility: Building Inclusive Websites",
    rating: 4.7,
    studentsEnrolled: 89,
    modules: 5,
    category: CATEGORIES.FRONTEND,
    description: "Learn how to create websites that are accessible to everyone, including people with disabilities. This course covers WCAG guidelines, semantic HTML, ARIA attributes, keyboard navigation, and testing tools. By the end of this course, you'll be able to build websites that comply with accessibility standards and provide a better experience for all users.",
    postedDate: "2024-10-28",
    postedTime: "10:15 AM",
    imageUrl: "/class2.png",
    status: "available",
    // Detailed category description
    categoryDescription: "frontend development, with a focus on accessibility standards and inclusive design principles. You'll learn how to create websites that can be used by everyone, including people with disabilities.",
    // Targets and goals specific to this class
    targetsAndGoals: {
      description: "This class is designed to help you understand and implement web accessibility standards. You'll learn through practical examples and real-world scenarios that will prepare you to create inclusive web experiences.",
      goals: [
        "Master accessibility principles and WCAG 2.1 guidelines",
        "Implement semantic HTML and ARIA attributes effectively",
        "Design keyboard-accessible navigation and focus management",
        "Create inclusive user interfaces for diverse abilities",
        "Develop skills in accessibility testing and auditing",
        "Build compliance-ready web applications"
      ]
    },
    // Learning tools required for this class
    learningTools: {
      deviceSpecs: {
        processor: "Intel Dual Core (Core i3 and above recommended)"
      },
      tools: [
        "Visual Studio Code",
        "Screen readers (NVDA, VoiceOver)",
        "Accessibility testing tools (Axe, WAVE)",
        "Lighthouse Accessibility Audits"
      ],
      additionalRequirements: []
    },
    // Module data for this class
    moduleData: [
      {
        title: "Introduction to Web Accessibility",
        description: "Understand the importance of web accessibility, disability types, and the legal requirements for inclusive design."
      },
      {
        title: "WCAG Guidelines and Compliance",
        description: "Master the Web Content Accessibility Guidelines (WCAG) 2.1 and learn how to implement AA compliance standards."
      },
      {
        title: "Semantic HTML and ARIA",
        description: "Learn proper semantic markup and ARIA attributes to create meaningful content structure for assistive technologies."
      },
      {
        title: "Keyboard Navigation and Focus Management",
        description: "Implement keyboard-accessible navigation patterns and proper focus management for interactive elements."
      },
      {
        title: "Testing and Auditing for Accessibility",
        description: "Use automated and manual testing tools to audit websites for accessibility compliance and user experience."
      }
    ]
  },
  {
    id: 103,
    title: "React.js: Building Modern User Interfaces",
    rating: 4.8,
    studentsEnrolled: 167,
    modules: 6,
    category: CATEGORIES.FRONTEND,
    description: "Master React.js, the popular JavaScript library for building user interfaces. Learn component-based architecture, state management, hooks, context API, and how to integrate with backend services. By the end of this course, you'll be able to build dynamic, responsive, and maintainable web applications using React.",
    postedDate: "2024-12-02",
    postedTime: "11:00 AM",
    imageUrl: "/class3.png",
    status: "available",
    // Detailed category description
    categoryDescription: "frontend development with React.js, focusing on component-based architecture and modern UI development. You'll learn to create dynamic, responsive web applications with the most popular JavaScript library for building user interfaces.",
    // Targets and goals specific to this class
    targetsAndGoals: {
      description: "This class is designed to help you master React.js and its ecosystem. You'll learn through hands-on projects and practical examples that will prepare you for real-world application development.",
      goals: [
        "Master React.js fundamentals and component-based architecture",
        "Implement modern state management with hooks and Context API",
        "Build responsive and interactive user interfaces",
        "Integrate React applications with RESTful APIs",
        "Create single-page applications with React Router",
        "Optimize React applications for performance and scalability"
      ]
    },
    // Learning tools required for this class
    learningTools: {
      deviceSpecs: {
        processor: "Intel Dual Core (Core i3 and above recommended)"
      },
      tools: [
        "Visual Studio Code",
        "Node.js",
        "npm or yarn",
        "React Developer Tools",
        "Git & GitHub"
      ],
      additionalRequirements: []
    },
    // Module data for this class
    moduleData: [
      {
        title: "React Fundamentals",
        description: "Learn JSX syntax, component creation, and the virtual DOM concepts that power React applications."
      },
      {
        title: "State and Props",
        description: "Master component state management, props passing, and data flow patterns in React applications."
      },
      {
        title: "Hooks and Functional Components",
        description: "Explore useState, useEffect, and custom hooks to manage state and side effects in functional components."
      },
      {
        title: "Routing and Navigation",
        description: "Implement client-side routing with React Router for creating seamless single-page applications."
      },
      {
        title: "State Management with Context API",
        description: "Learn global state management using Context API and useReducer for complex application state."
      },
      {
        title: "API Integration and Deployment",
        description: "Connect React apps to backend APIs, handle async operations, and deploy to production platforms."
      }
    ]
  }
];

// Backend classes
const backendClasses = [
  {
    id: 201,
    title: "Node.js Backend Development",
    rating: 4.8,
    studentsEnrolled: 103,
    modules: 6,
    category: CATEGORIES.BACKEND,
    description: "Master server-side JavaScript with Node.js. This comprehensive course covers everything from basic Node.js concepts to building production-ready RESTful APIs and real-time applications. Learn about Express.js, MongoDB integration, authentication, testing, and deployment strategies. By the end of this course, you'll be able to build scalable and maintainable backend applications using Node.js.",
    postedDate: "2024-11-08",
    postedTime: "2:00 PM",
    imageUrl: "/class4.png",
    status: "available",
    // Detailed category description
    categoryDescription: "backend development, covering server-side programming, API design, database management, and scalable architecture. You'll develop skills in building robust, secure, and efficient server applications using Node.js and its ecosystem.",
    // Targets and goals specific to this class
    targetsAndGoals: {
      description: "This class is designed to help you master Node.js and server-side JavaScript. You'll learn through hands-on projects and practical examples that will prepare you for real-world backend development.",
      goals: [
        "Master server-side JavaScript programming with Node.js",
        "Build RESTful APIs using Express.js framework",
        "Implement database integration with MongoDB and Mongoose",
        "Create secure authentication and authorization systems",
        "Develop real-time applications with WebSocket technology",
        "Deploy and scale Node.js applications in production"
      ]
    },
    // Learning tools required for this class
    learningTools: {
      deviceSpecs: {
        processor: "Intel Dual Core (Core i3 and above recommended)"
      },
      tools: [
        "Visual Studio Code",
        "Node.js",
        "Postman",
        "MongoDB Compass",
        "Git & GitHub"
      ],
      additionalRequirements: []
    },
    // Module data for this class
    moduleData: [
      {
        title: "Node.js Fundamentals",
        description: "Learn Node.js runtime, event loop, modules, and file system operations for server-side development."
      },
      {
        title: "Express.js Framework",
        description: "Master Express.js for building web servers, handling routes, middleware, and HTTP request/response cycles."
      },
      {
        title: "Database Integration with MongoDB",
        description: "Connect Node.js applications to MongoDB, design schemas, and perform CRUD operations with Mongoose."
      },
      {
        title: "Authentication & Security",
        description: "Implement JWT authentication, password hashing, input validation, and security best practices."
      },
      {
        title: "Real-time Applications",
        description: "Build real-time features using WebSockets, Socket.io, and event-driven architecture patterns."
      },
      {
        title: "Testing & Deployment",
        description: "Write unit tests, integration tests, and deploy Node.js applications to cloud platforms."
      }
    ]
  },
  {
    id: 202,
    title: "Python Django: Web Development",
    rating: 4.6,
    studentsEnrolled: 124,
    modules: 7,
    category: CATEGORIES.BACKEND,
    description: "Learn web development with Python and Django, a high-level web framework that encourages rapid development and clean, pragmatic design. This course covers models, views, templates, forms, authentication, and deployment. By the end of this course, you'll be able to build robust web applications using Django's powerful features.",
    postedDate: "2024-10-15",
    postedTime: "3:30 PM",
    imageUrl: "/class5.png",
    status: "available",
    // Detailed category description
    categoryDescription: "backend development with Python and Django, focusing on rapid development and clean design. You'll learn to build robust web applications using Django's powerful features and Python's elegant syntax.",
    // Targets and goals specific to this class
    targetsAndGoals: {
      description: "This class is designed to help you master Python web development with Django. You'll learn through hands-on projects and practical examples that will prepare you for real-world application development.",
      goals: [
        "Master Python programming for web development",
        "Build web applications using Django framework",
        "Implement the MVT (Model-View-Template) architecture",
        "Create secure authentication and authorization systems",
        "Design and manage database-driven applications",
        "Deploy Django applications to production environments"
      ]
    },
    // Learning tools required for this class
    learningTools: {
      deviceSpecs: {
        processor: "Intel Dual Core (Core i3 and above recommended)"
      },
      tools: [
        "Visual Studio Code",
        "Python 3.9+",
        "Django",
        "PostgreSQL",
        "Git & GitHub"
      ],
      additionalRequirements: []
    },
    // Module data for this class
    moduleData: [
      {
        title: "Python Fundamentals for Web Development",
        description: "Master Python syntax, data structures, and object-oriented programming concepts essential for Django development."
      },
      {
        title: "Django Basics and Project Structure",
        description: "Understand Django's MVT architecture, project setup, and configuration for building scalable web applications."
      },
      {
        title: "Models and Database Integration",
        description: "Design database models, relationships, and migrations using Django ORM for data persistence."
      },
      {
        title: "Views and Templates",
        description: "Create dynamic web pages using Django views, templates, and the template language for content rendering."
      },
      {
        title: "Forms and User Input",
        description: "Handle user input, form validation, and data processing using Django's form framework."
      },
      {
        title: "Authentication and Authorization",
        description: "Implement user registration, login, permissions, and role-based access control in Django applications."
      },
      {
        title: "Deployment and Production",
        description: "Deploy Django applications using Docker, configure production settings, and manage static files."
      }
    ]
  },
  {
    id: 203,
    title: "RESTful API Design and Development",
    rating: 4.7,
    studentsEnrolled: 87,
    modules: 5,
    category: CATEGORIES.BACKEND,
    description: "Master the principles of RESTful API design and development. Learn how to create scalable, maintainable, and secure APIs that follow REST principles. This course covers resource modeling, HTTP methods, status codes, authentication, documentation, and testing. By the end of this course, you'll be able to design and implement professional-grade APIs.",
    postedDate: "2024-09-20",
    postedTime: "1:45 PM",
    imageUrl: "/class6.png",
    status: "available",
    // Detailed category description
    categoryDescription: "backend development with a focus on API design and implementation. You'll learn to create scalable, maintainable, and secure APIs that follow REST principles and best practices.",
    // Targets and goals specific to this class
    targetsAndGoals: {
      description: "This class is designed to help you master RESTful API design and development. You'll learn through hands-on projects and practical examples that will prepare you for creating professional-grade APIs.",
      goals: [
        "Master RESTful architecture principles and constraints",
        "Design scalable and maintainable API endpoints",
        "Implement proper HTTP methods and status codes",
        "Create secure authentication and authorization systems",
        "Build comprehensive API documentation with OpenAPI",
        "Test and optimize API performance and reliability"
      ]
    },
    // Learning tools required for this class
    learningTools: {
      deviceSpecs: {
        processor: "Intel Dual Core (Core i3 and above recommended)"
      },
      tools: [
        "Visual Studio Code",
        "Postman",
        "Swagger/OpenAPI",
        "Insomnia",
        "Git & GitHub"
      ],
      additionalRequirements: []
    },
    // Module data for this class
    moduleData: [
      {
        title: "RESTful Architecture Principles",
        description: "Understand REST constraints, statelessness, and architectural patterns for building scalable APIs."
      },
      {
        title: "Resource Modeling and URL Design",
        description: "Design intuitive resource hierarchies, URL structures, and naming conventions for RESTful endpoints."
      },
      {
        title: "HTTP Methods and Status Codes",
        description: "Master proper usage of HTTP verbs, response codes, and headers for effective API communication."
      },
      {
        title: "Authentication and Security",
        description: "Implement JWT tokens, OAuth 2.0, rate limiting, and security best practices for API protection."
      },
      {
        title: "Documentation and Testing",
        description: "Create interactive API documentation with OpenAPI and implement comprehensive testing strategies."
      }
    ]
  }
];

// Mobile classes
const mobileClasses = [
  {
    id: 301,
    title: "Flutter Mobile App Development",
    rating: 4.9,
    studentsEnrolled: 145,
    modules: 8,
    category: CATEGORIES.MOBILE,
    description: "Learn to build beautiful, natively compiled applications for mobile, web, and desktop from a single codebase with Flutter. This course covers Dart programming language, Flutter widgets, state management, navigation, and integration with backend services. By the end of this course, you'll be able to create cross-platform mobile applications with Flutter.",
    postedDate: "2024-12-10",
    postedTime: "10:00 AM",
    imageUrl: "/class7.png",
    status: "available",
    // Detailed category description
    categoryDescription: "mobile app development, exploring cross-platform solutions with Flutter. You'll learn to create responsive, feature-rich applications that provide excellent user experiences across different devices using a single codebase.",
    // Targets and goals specific to this class
    targetsAndGoals: {
      description: "This class is designed to help you master Flutter and Dart for cross-platform mobile development. You'll learn through hands-on projects and practical examples that will prepare you for real-world app development.",
      goals: [
        "Master cross-platform mobile development with Flutter",
        "Learn Dart programming language fundamentals",
        "Build responsive UI with Flutter widgets and Material Design",
        "Implement effective state management patterns",
        "Integrate mobile apps with backend services and APIs",
        "Deploy applications to Google Play and App Store"
      ]
    },
    // Learning tools required for this class
    learningTools: {
      deviceSpecs: {
        processor: "Intel Dual Core (Core i3 and above recommended)"
      },
      tools: [
        "Android Studio / Visual Studio Code",
        "Flutter SDK",
        "Dart SDK",
        "Android/iOS Emulator"
      ],
      additionalRequirements: [
        "Smartphone - Android or iOS device for testing"
      ]
    },
    // Module data for this class
    moduleData: [
      {
        title: "Mobile Development Fundamentals",
        description: "Understand mobile app architecture, platform differences, and cross-platform development concepts."
      },
      {
        title: "Dart Programming Language",
        description: "Master Dart syntax, object-oriented programming, and asynchronous programming for Flutter development."
      },
      {
        title: "Flutter Widgets and UI",
        description: "Build responsive user interfaces using Flutter's widget system, layouts, and Material Design components."
      },
      {
        title: "Navigation & State Management",
        description: "Implement navigation patterns and state management solutions like Provider, Bloc, and Riverpod."
      },
      {
        title: "Data Persistence & API Integration",
        description: "Handle local data storage with SQLite and integrate with REST APIs for dynamic content."
      },
      {
        title: "Native Device Features",
        description: "Access device features like camera, GPS, sensors, and platform-specific functionality."
      },
      {
        title: "Testing and Debugging",
        description: "Write unit tests, widget tests, and integration tests while mastering debugging techniques."
      },
      {
        title: "App Store Deployment",
        description: "Prepare, build, and deploy Flutter applications to Google Play Store and Apple App Store."
      }
    ]
  },
  {
    id: 302,
    title: "React Native: Cross-Platform Mobile Development",
    rating: 4.7,
    studentsEnrolled: 128,
    modules: 7,
    category: CATEGORIES.MOBILE,
    description: "Master React Native to build mobile applications for iOS and Android using JavaScript and React. This course covers components, navigation, state management, native modules, and deployment. By the end of this course, you'll be able to create high-performance, cross-platform mobile applications with a native feel.",
    postedDate: "2024-11-22",
    postedTime: "2:30 PM",
    imageUrl: "/class8.png",
    status: "available",
    // Detailed category description
    categoryDescription: "mobile app development using React Native, a framework for building native apps using React and JavaScript. You'll learn to create cross-platform mobile applications that have the performance of native apps with the development efficiency of web applications.",
    // Targets and goals specific to this class
    targetsAndGoals: {
      description: "This class is designed to help you master React Native for cross-platform mobile development. You'll learn through hands-on projects and practical examples that will prepare you for real-world app development.",
      goals: [
        "Build native mobile apps using React and JavaScript",
        "Master React Native components and styling systems",
        "Implement navigation and routing patterns",
        "Manage application state with Redux and Context",
        "Integrate with device APIs and native modules",
        "Deploy applications to Google Play and App Store"
      ]
    },
    // Learning tools required for this class
    learningTools: {
      deviceSpecs: {
        processor: "Intel Dual Core (Core i3 and above recommended)"
      },
      tools: [
        "Visual Studio Code",
        "Node.js",
        "React Native CLI",
        "Expo CLI",
        "Android/iOS Emulator"
      ],
      additionalRequirements: [
        "Smartphone - Android or iOS device for testing"
      ]
    },
    // Module data for this class
    moduleData: [
      {
        title: "React Native Fundamentals",
        description: "Learn React Native architecture, setup, and how it differs from React for web development."
      },
      {
        title: "Components and Styling",
        description: "Master React Native components, styling with StyleSheet, and responsive design principles."
      },
      {
        title: "Navigation & Routing",
        description: "Implement navigation patterns using React Navigation for stack, tab, and drawer navigation."
      },
      {
        title: "State Management",
        description: "Manage application state using Redux, Context API, and local component state effectively."
      },
      {
        title: "API Integration & Networking",
        description: "Connect to REST APIs, handle network requests, and manage data synchronization."
      },
      {
        title: "Native Device Features",
        description: "Access device features like camera, location, push notifications, and native modules."
      },
      {
        title: "Testing & Deployment",
        description: "Write tests for React Native apps and deploy to app stores using Expo and native builds."
      }
    ]
  },
  {
    id: 303,
    title: "iOS Development with Swift",
    rating: 4.8,
    studentsEnrolled: 96,
    modules: 6,
    category: CATEGORIES.MOBILE,
    description: "Learn iOS app development using Swift, Apple's powerful and intuitive programming language. This course covers Swift fundamentals, UIKit, SwiftUI, Core Data, networking, and app deployment. By the end of this course, you'll be able to build professional iOS applications and publish them to the App Store.",
    postedDate: "2024-10-05",
    postedTime: "11:15 AM",
    imageUrl: "/class9.png",
    status: "available",
    // Detailed category description
    categoryDescription: "mobile app development for iOS using Swift, Apple's modern programming language. You'll learn to create native iOS applications with beautiful interfaces and smooth performance that follow Apple's design guidelines.",
    // Targets and goals specific to this class
    targetsAndGoals: {
      description: "This class is designed to help you master iOS development with Swift. You'll learn through hands-on projects and practical examples that will prepare you for real-world app development.",
      goals: [
        "Master native iOS app development with Swift",
        "Learn Swift programming language and iOS frameworks",
        "Build user interfaces with UIKit and SwiftUI",
        "Implement data persistence and Core Data integration",
        "Integrate with Apple's ecosystem and device features",
        "Deploy applications to the Apple App Store"
      ]
    },
    // Learning tools required for this class
    learningTools: {
      deviceSpecs: {
        processor: "Intel Dual Core (Core i3 and above recommended)"
      },
      tools: [
        "Xcode",
        "Swift Playgrounds",
        "iOS Simulator",
        "TestFlight"
      ],
      additionalRequirements: [
        "iPhone or iPad for testing (recommended)",
        "Mac computer for Xcode (required)"
      ]
    },
    // Module data for this class
    moduleData: [
      {
        title: "Swift Programming Fundamentals",
        description: "Master Swift syntax, optionals, protocols, and object-oriented programming concepts for iOS development."
      },
      {
        title: "UIKit & Interface Design",
        description: "Build user interfaces using UIKit, Auto Layout, and Interface Builder for responsive iOS apps."
      },
      {
        title: "SwiftUI & Modern UI Development",
        description: "Create declarative user interfaces with SwiftUI and learn modern iOS design patterns."
      },
      {
        title: "Data Persistence & Core Data",
        description: "Implement local data storage using Core Data, UserDefaults, and file system operations."
      },
      {
        title: "Networking & API Integration",
        description: "Connect iOS apps to web services, handle JSON data, and implement network communication."
      },
      {
        title: "App Store Deployment",
        description: "Prepare iOS apps for submission, handle app store guidelines, and manage app distribution."
      }
    ]
  }
];

// Combine all classes into one array
export const dummyAvailableClassesData = [
  ...frontendClasses,
  ...backendClasses,
  ...mobileClasses
];

// Export categories and class arrays for direct access
export const categories = CATEGORIES;
export const classesByCategory = {
  frontend: frontendClasses,
  backend: backendClasses,
  mobile: mobileClasses
};

export default dummyAvailableClassesData;
