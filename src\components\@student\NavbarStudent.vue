<template>
  <nav class="sticky top-0 z-50 bg-white shadow-sm">
    <!-- Main navbar wrapper with responsive adjustments -->
    <div
      class="w-full transition-all duration-300 ease-in-out relative bg-white"
      :class="[
        {
          'md:ml-64': isSidebarOpen,
          'ml-0': !isSidebarOpen && !isSidebarOpen
        }
      ]"
    >
      <!-- Border that adjusts with content width -->
      <div class="absolute bottom-0 left-0 right-0 h-[1px] bg-gray-200 transition-all duration-300 ease-in-out"></div>

      <!-- Full-width background to ensure consistency -->
      <div class="absolute top-0 right-0 w-screen h-full bg-white -z-10"></div>

      <!-- Desktop Toggle Button with Enhanced Hover Interaction - Only visible on desktop -->
      <div
        class="fixed left-0 top-0 bottom-0 w-10 z-50 hidden md:flex items-center"
        @mouseenter="showHoverToggle = true"
        @mouseleave="handleHoverLeave"
        v-if="!isSidebarOpen"
        data-testid="sidebar-hover-area"
      >
        <!-- Hover-sensitive area with enhanced indicator for right arrow -->
        <div
          class="w-full h-full flex items-center justify-center"
          :class="{'bg-gradient-to-r from-gray-100/70 to-transparent': showHoverToggle}"
        >
          <!-- Toggle button with enhanced position animation for right arrow -->
          <div
            class="absolute left-2 transition-all duration-300 transform"
            :class="{
              'opacity-100 translate-x-0 scale-100': showHoverToggle,
              'opacity-0 -translate-x-4 scale-90': !showHoverToggle
            }"
          >
            <SidebarToggle
              @toggle="toggleSidebar"
              :is-open="false"
              class="filter drop-shadow-sm hover:drop-shadow-md hover:-translate-y-1 transition-all duration-300 ease-in-out group-hover:animate-none animate-subtle-pulse"
              @mouseenter="showHoverToggle = true"
            />
          </div>
        </div>
      </div>

      <!-- Logo - Visible only when sidebar is closed - Improved mobile positioning -->
      <div
        class="absolute top-0 left-12 sm:left-14 flex items-center h-16 z-20 transition-all duration-300 ease-in-out"
        :class="{'opacity-0 invisible md:pointer-events-none': isSidebarOpen, 'opacity-100 visible pointer-events-auto': !isSidebarOpen}"
      >
        <div class="flex items-center transition-all duration-300 ease-in-out">
          <router-link to="/" class="flex items-center group">
            <img
              src="/logo.png"
              alt="FlowCamp Logo"
              class="h-7 sm:h-8 transition-transform duration-300 group-hover:scale-105"
            />
            <span class="ml-1 sm:ml-2 text-orange-500 font-bold text-sm sm:text-base transition-all duration-300 group-hover:text-orange-600">
              Flow Camp
            </span>
          </router-link>
        </div>
      </div>

      <!-- Main navbar content with responsive adjustments -->
      <div
        class="w-full flex flex-wrap items-center justify-between h-16 transition-all duration-300 ease-in-out relative z-10"
        :class="[
          {
            'px-2 sm:px-6 md:px-8': true,
            'pl-12 sm:pl-20 md:pl-56': !isSidebarOpen, /* Improved mobile spacing */
            'pl-4 sm:pl-64 md:pl-72': isSidebarOpen
          }
        ]">

      <!-- Navigation Links - Only visible on desktop -->
      <div class="hidden md:flex md:flex-1 md:justify-center order-1 transition-all duration-300 ease-in-out">
        <ul class="flex flex-row space-x-8 font-medium">
          <li class="relative">
            <router-link
              to="/student/dashboard"
              class="block py-2 px-3 rounded transition-all duration-200 hover:text-orange"
              :class="isHomeActive ? 'text-orange font-semibold' : 'text-gray-700'"
            >
              Home
              <div v-if="isHomeActive" class="absolute h-0.5 bg-orange w-full bottom-[-5px] left-0 transition-all duration-300"></div>
            </router-link>
          </li>
          <li class="relative">
            <router-link
              to="/student/academy"
              class="block py-2 px-3 rounded transition-all duration-200 hover:text-orange"
              :class="isAcademyActive ? 'text-orange font-semibold' : 'text-gray-700'"
              @click="resetNavigationState"
            >
              Academy
              <div v-if="isAcademyActive" class="absolute h-0.5 bg-orange w-full bottom-[-5px] left-0 transition-all duration-300"></div>
            </router-link>
          </li>
          <li class="relative">
            <router-link
              to="/student/classes"
              class="block py-2 px-3 rounded transition-all duration-200 hover:text-orange"
              :class="isClassesActive ? 'text-orange font-semibold' : 'text-gray-700'"
              @click="resetNavigationState"
            >
              Classes
              <div v-if="isClassesActive" class="absolute h-0.5 bg-orange w-full bottom-[-5px] left-0 transition-all duration-300"></div>
            </router-link>
          </li>
        </ul>
      </div>

      <!-- Right Side - Notifications and Profile (always visible) -->
      <div class="flex items-center ml-auto space-x-1 sm:space-x-3 md:space-x-4 order-2">
        <!-- Notification Bell with improved hover effect and mobile optimization -->
        <div class="relative">
          <button
            @click.stop="toggleNotifications"
            class="notification-bell relative p-1.5 sm:p-2 rounded-full transition-all duration-200 group"
            :class="[
              showNotifications ? 'bg-orange-100 text-orange' : 'text-gray-700 hover:text-orange hover:bg-orange-50',
              unreadNotificationsCount > 0 ? 'animate-pulse-once' : ''
            ]"
            aria-label="Notifications"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              class="h-5 w-5 sm:h-6 sm:w-6 transition-transform duration-200 group-hover:scale-110"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 17h5l-1.405-1.405A2.032 2.032 0 0118 14.158V11a6.002 6.002 0 00-4-5.659V5a2 2 0 10-4 0v.341C7.67 6.165 6 8.388 6 11v3.159c0 .538-.214 1.055-.595 1.436L4 17h5m6 0v1a3 3 0 11-6 0v-1m6 0H9" />
            </svg>
            <!-- Notification badge - only show unread count with animation -->
            <transition
              enter-active-class="transition-all duration-300 ease-in-out"
              enter-from-class="opacity-0 scale-50"
              enter-to-class="opacity-100 scale-100"
              leave-active-class="transition-all duration-200 ease-in-out"
              leave-from-class="opacity-100 scale-100"
              leave-to-class="opacity-0 scale-50"
            >
              <span
                v-if="unreadNotificationsCount > 0"
                class="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full min-w-[18px] h-[18px] flex items-center justify-center px-1 shadow-sm transform origin-center"
              >
                {{ unreadNotificationsCount > 9 ? '9+' : unreadNotificationsCount }}
              </span>
            </transition>
          </button>

          <!-- Notification Dropdown with transition - Improved mobile responsiveness -->
          <transition
            enter-active-class="transition ease-out duration-200"
            enter-from-class="transform opacity-0 scale-95"
            enter-to-class="transform opacity-100 scale-100"
            leave-active-class="transition ease-in duration-150"
            leave-from-class="transform opacity-100 scale-100"
            leave-to-class="transform opacity-0 scale-95"
          >
            <div
              v-show="showNotifications"
              class="fixed sm:absolute inset-x-0 top-16 sm:top-auto sm:right-0 sm:left-auto sm:mt-2 w-full sm:w-[400px] md:w-96 bg-white rounded-none sm:rounded-lg shadow-xl border-t sm:border border-gray-200 z-50 overflow-hidden max-h-[80vh] sm:max-h-[500px] flex flex-col"
              ref="notificationDropdown"
              @click.stop
            >
              <!-- Header with Mark all as read option and notification count -->
              <div class="p-3 flex justify-between items-center bg-gray-50 border-b border-gray-200">
                <div class="flex items-center">
                  <h3 class="text-base font-semibold text-gray-800">Notifications</h3>
                  <!-- Notification count badge -->
                  <div
                    v-if="notifications.length > 0"
                    class="ml-2 px-1.5 py-0.5 bg-gray-200 text-gray-700 text-xs rounded-full"
                  >
                    {{ notifications.length }}
                  </div>
                  <!-- Unread count badge -->
                  <div
                    v-if="unreadNotificationsCount > 0"
                    class="ml-1 px-1.5 py-0.5 bg-orange text-white text-xs rounded-full flex items-center"
                  >
                    <span class="w-1 h-1 bg-white rounded-full mr-1"></span>
                    {{ unreadNotificationsCount }} new
                  </div>
                </div>

                <div class="flex items-center space-x-2">
                  <button
                    v-if="unreadNotificationsCount > 0"
                    @click.stop="markAllAsRead"
                    class="text-xs text-orange hover:text-orange-600 font-medium transition-colors flex items-center"
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                    </svg>
                    Mark all as read
                  </button>

                  <!-- Close button for notification dropdown -->
                  <button
                    @click.stop="toggleNotifications"
                    class="p-1 rounded-full text-gray-400 hover:text-gray-600 hover:bg-gray-100 transition-colors"
                    aria-label="Close notifications"
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                    </svg>
                  </button>
                </div>
              </div>

              <!-- Notification Items -->
              <div v-if="notifications.length > 0" class="flex-1 overflow-y-auto scrollbar-thin scrollbar-track-gray-100 scrollbar-thumb-gray-300 scrollbar-w-2">
                <!-- Unread Notifications Section -->
                <div v-if="unreadNotifications.length > 0">
                  <div class="px-3 py-1 bg-gray-100 text-xs font-medium text-gray-600">
                    Unread Notifications
                  </div>
                  <div
                    v-for="(notification, index) in unreadNotifications"
                    :key="'unread-' + index"
                    class="group border-b border-gray-100 last:border-b-0 transition-all duration-200 relative bg-white hover:bg-orange-50/30"
                  >
                    <!-- Clickable area with proper padding -->
                    <div
                      @click="navigateToNotification(notification)"
                      class="p-3 cursor-pointer relative overflow-hidden hover:bg-gray-50/80 transition-colors duration-200"
                    >
                      <!-- Clear button for individual notification - Positioned at top-right -->
                      <button
                        @click.stop="clearSingleNotification(notification, index)"
                        class="absolute top-2 right-2 p-1.5 rounded-full text-gray-400 hover:text-gray-600 hover:bg-gray-100 transition-colors z-10"
                        aria-label="Clear notification"
                      >
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                        </svg>
                      </button>

                      <!-- Right arrow indicator positioned at center-right -->
                      <div class="absolute top-1/2 right-2 -translate-y-1/2 p-1.5 opacity-50 sm:opacity-0 group-hover:opacity-100 transition-all duration-200 ease-in-out z-10">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 text-orange flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                        </svg>
                      </div>

                      <!-- Unread indicator bar -->
                      <div
                        class="absolute left-0 top-1/2 transform -translate-y-1/2 w-1 h-10 rounded-r-full transition-all duration-200 bg-orange"
                      ></div>

                      <!-- Simplified content with essential information -->
                      <div class="flex flex-col pl-3 pr-8 min-h-[60px] w-full">
                        <!-- Title with NEW badge -->
                        <div class="flex items-center space-x-2 w-full">
                          <p
                            class="text-sm font-semibold text-gray-800 truncate max-w-[65%] sm:max-w-[75%]"
                            :title="notification.title"
                          >
                            {{ notification.title }}
                          </p>

                          <!-- New indicator for unread notifications -->
                          <span
                            class="text-[10px] px-1 py-0.5 bg-orange text-white rounded-sm font-medium flex-shrink-0"
                          >
                            NEW
                          </span>
                        </div>

                        <!-- Class name for context -->
                        <p
                          class="text-xs text-gray-500 mt-1 truncate w-full"
                          :title="notification.className"
                        >
                          {{ notification.className }}
                        </p>

                        <!-- Timestamp -->
                        <div class="flex items-center mt-2">
                          <svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3 text-gray-400 mr-1 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                          </svg>
                          <p class="text-xs text-gray-400" :title="notification.time">{{ notification.time }}</p>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- Read Notifications Section -->
                <div v-if="readNotifications.length > 0">
                  <div class="px-3 py-1 bg-gray-100 text-xs font-medium text-gray-600 mt-2">
                    Read Notifications
                  </div>
                  <div
                    v-for="(notification, index) in readNotifications"
                    :key="'read-' + index"
                    class="group border-b border-gray-100 last:border-b-0 transition-all duration-200 relative bg-gray-50/50 hover:bg-gray-50"
                  >
                    <!-- Clickable area with proper padding -->
                    <div
                      @click="navigateToNotification(notification)"
                      class="p-3 cursor-pointer relative overflow-hidden hover:bg-gray-50/80 transition-colors duration-200"
                    >
                      <!-- Clear button for individual notification - Positioned at top-right -->
                      <button
                        @click.stop="clearSingleNotification(notification, index)"
                        class="absolute top-2 right-2 p-1.5 rounded-full text-gray-400 hover:text-gray-600 hover:bg-gray-100 transition-colors z-10"
                        aria-label="Clear notification"
                      >
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                        </svg>
                      </button>

                      <!-- Right arrow indicator positioned at center-right -->
                      <div class="absolute top-1/2 right-2 -translate-y-1/2 p-1.5 opacity-50 sm:opacity-0 group-hover:opacity-100 transition-all duration-200 ease-in-out z-10">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 text-gray-400 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                        </svg>
                      </div>

                      <!-- Transparent indicator bar for read notifications -->
                      <div
                        class="absolute left-0 top-1/2 transform -translate-y-1/2 w-1 h-10 rounded-r-full transition-all duration-200 bg-transparent"
                      ></div>

                      <!-- Simplified content with essential information -->
                      <div class="flex flex-col pl-3 pr-8 min-h-[60px] w-full">
                        <!-- Title -->
                        <div class="flex items-center space-x-2 w-full">
                          <p
                            class="text-sm text-gray-600 truncate max-w-[85%] sm:max-w-[90%]"
                            :title="notification.title"
                          >
                            {{ notification.title }}
                          </p>
                        </div>

                        <!-- Class name for context -->
                        <p
                          class="text-xs text-gray-500 mt-1 truncate w-full"
                          :title="notification.className"
                        >
                          {{ notification.className }}
                        </p>

                        <!-- Timestamp -->
                        <div class="flex items-center mt-2">
                          <svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3 text-gray-400 mr-1 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                          </svg>
                          <p class="text-xs text-gray-400" :title="notification.time">{{ notification.time }}</p>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- Footer with Clear all button and improved styling -->
                <div class="p-3 bg-gray-50 border-t border-gray-200 flex justify-between items-center">
                  <span class="text-xs text-gray-500">
                    {{ notifications.length }} notification{{ notifications.length !== 1 ? 's' : '' }}
                  </span>
                  <button
                    @click.stop="clearAllNotifications"
                    class="text-xs text-gray-600 hover:text-gray-800 font-medium transition-colors flex items-center"
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                    </svg>
                    Clear all
                  </button>
                </div>
              </div>

              <!-- Empty State with improved styling and animation -->
              <div v-else class="py-12 px-4 text-center flex-1 flex flex-col items-center justify-center">
                <div class="w-20 h-20 mx-auto mb-5 rounded-full bg-gray-50 flex items-center justify-center relative overflow-hidden">
                  <!-- Bell icon with subtle animation -->
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-10 w-10 text-gray-300 transform transition-transform duration-1000 hover:scale-110" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M15 17h5l-1.405-1.405A2.032 2.032 0 0118 14.158V11a6.002 6.002 0 00-4-5.659V5a2 2 0 10-4 0v.341C7.67 6.165 6 8.388 6 11v3.159c0 .538-.214 1.055-.595 1.436L4 17h5m6 0v1a3 3 0 11-6 0v-1m6 0H9" />
                  </svg>

                  <!-- Decorative circles -->
                  <div class="absolute w-full h-full">
                    <div class="absolute top-1/4 left-1/4 w-1 h-1 bg-gray-200 rounded-full"></div>
                    <div class="absolute top-3/4 right-1/4 w-1.5 h-1.5 bg-gray-200 rounded-full"></div>
                    <div class="absolute bottom-1/4 right-1/3 w-1 h-1 bg-gray-200 rounded-full"></div>
                  </div>
                </div>
                <h4 class="text-gray-700 font-medium mb-2">All caught up!</h4>
                <p class="text-gray-500 mb-1">There are no new notifications for you yet.</p>
                <p class="text-xs text-gray-400 max-w-[250px] mx-auto">
                  We'll notify you when there are new assignments or updates.
                </p>
              </div>
            </div>
          </transition>
        </div>

        <!-- Profile Dropdown - Improved for mobile -->
        <div class="relative">
          <button
            @click.stop="toggleProfileDropdown"
            class="flex items-center space-x-1 focus:outline-none p-1.5 rounded-full hover:bg-orange-50 transition-colors"
            aria-label="User profile"
          >
            <img src="/prof.png" alt="User Profile" class="h-7 w-7 sm:h-8 sm:w-8 rounded-full object-cover border border-gray-300" />
            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 text-gray-500 hidden sm:block" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
            </svg>
          </button>

          <!-- Profile Dropdown Menu with transition -->
          <transition
            enter-active-class="transition ease-out duration-200"
            enter-from-class="transform opacity-0 scale-95"
            enter-to-class="transform opacity-100 scale-100"
            leave-active-class="transition ease-in duration-150"
            leave-from-class="transform opacity-100 scale-100"
            leave-to-class="transform opacity-0 scale-95"
          >
            <div
              v-show="showProfileDropdown"
              class="absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-lg border border-gray-200 z-50 overflow-hidden"
              ref="profileDropdown"
              @click.stop
            >
              <!-- User info section -->
              <div class="px-4 py-3 border-b border-gray-100">
                <div class="flex items-center">
                  <img src="/prof.png" alt="User Profile" class="h-8 w-8 rounded-full object-cover border border-gray-200" />
                  <div class="ml-3">
                    <p class="text-sm font-medium text-gray-800 truncate">{{ userName }}</p>
                    <p class="text-xs text-gray-500">Student</p>
                  </div>
                </div>
              </div>

              <!-- Menu items -->
              <div class="py-1">
                <router-link
                  to="/student/settings"
                  class="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-orange-50 hover:text-orange-600 transition-colors"
                  @click="showProfileDropdown = false"
                >
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-2 text-gray-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                  </svg>
                  Settings
                </router-link>
                <button
                  @click="logout"
                  class="flex items-center w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-orange-50 hover:text-orange-600 transition-colors"
                >
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-2 text-gray-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1" />
                  </svg>
                  Logout
                </button>
              </div>
            </div>
          </transition>
        </div>

        <!-- Sidebar toggle button for mobile - Only visible when sidebar is closed - Enhanced for mobile -->
        <button
          @click="toggleSidebar"
          type="button"
          class="inline-flex items-center justify-center p-2 ml-1 text-gray-600 rounded-lg md:hidden hover:bg-orange-50 focus:outline-none focus:ring-2 focus:ring-orange-300 active:scale-95 transition-all duration-200"
          aria-controls="sidebar-menu"
          :aria-expanded="isSidebarOpen"
          v-if="!isSidebarOpen"
        >
          <span class="sr-only">Open sidebar menu</span>
          <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
          </svg>
        </button>
      </div>

      </div>
    </div>
  </nav>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted, watch } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { STORAGE_KEYS, useClassStore } from '@/data/availableClasses';
import SidebarToggle from './SidebarToggle.vue';
import { toggleSidebar, isSidebarOpen } from '@/data/sidebarState';
import { formatDate, formatTime } from '@/utils/studentUtils';

const route = useRoute();
const router = useRouter();
const classStore = useClassStore();
const showNotifications = ref(false);
const notificationDropdown = ref(null);
const showHoverToggle = ref(false);
const showProfileDropdown = ref(false);
const profileDropdown = ref(null);
const userName = ref('Student User');

// Storage keys for notifications
const NOTIFICATION_STORAGE_KEY = 'flowcamp-notifications';
const CLEARED_NOTIFICATIONS_KEY = 'flowcamp-cleared-notifications';

// Notification state
const notifications = ref([]);
const clearedNotifications = ref([]);

// Load notifications from localStorage or generate from class data
const loadNotifications = () => {
  // Make sure cleared notifications are loaded first
  if (clearedNotifications.value.length === 0) {
    loadClearedNotifications();
  }

  const storedNotifications = localStorage.getItem(NOTIFICATION_STORAGE_KEY);

  if (storedNotifications) {
    // Parse stored notifications
    const parsedNotifications = JSON.parse(storedNotifications);

    // Filter out any notifications that might have been cleared since they were stored
    const filteredNotifications = parsedNotifications.filter(notification => {
      const identifier = getNotificationIdentifier(notification);
      return !clearedNotifications.value.includes(identifier);
    });

    // Update the notifications
    notifications.value = filteredNotifications;

    // If notifications were filtered, update localStorage
    if (filteredNotifications.length !== parsedNotifications.length) {
      console.log(`Filtered out ${parsedNotifications.length - filteredNotifications.length} cleared notifications on load`);
      saveNotifications();
    }
  } else {
    // Generate notifications from class materials with tasks
    generateNotificationsFromClasses();
  }
};

// Load cleared notifications from localStorage
const loadClearedNotifications = () => {
  const storedClearedNotifications = localStorage.getItem(CLEARED_NOTIFICATIONS_KEY);

  if (storedClearedNotifications) {
    clearedNotifications.value = JSON.parse(storedClearedNotifications);
  } else {
    clearedNotifications.value = [];
  }
};

// Helper functions for date checking
const isToday = (date) => {
  const today = new Date();
  return date.getDate() === today.getDate() &&
         date.getMonth() === today.getMonth() &&
         date.getFullYear() === today.getFullYear();
};

const isYesterday = (date) => {
  const yesterday = new Date();
  yesterday.setDate(yesterday.getDate() - 1);
  return date.getDate() === yesterday.getDate() &&
         date.getMonth() === yesterday.getMonth() &&
         date.getFullYear() === yesterday.getFullYear();
};

// Generate notifications from class materials
const generateNotificationsFromClasses = () => {
  const classes = classStore.classes.value;
  const newNotifications = [];

  // Get the current date for comparison
  const now = new Date();
  const yesterday = new Date(now);
  yesterday.setDate(yesterday.getDate() - 1);

  // Format dates for display
  const yesterdayStr = 'Yesterday';

  classes.forEach(classItem => {
    // Only include materials that have been marked as read (isRead === true)
    // This ensures notifications are only generated for materials the student has read
    classItem.materials.forEach(material => {
      if (material.isRead === true) {
        // Determine the most relevant date for the notification based on task status
        let relevantDate, relevantTime, notificationTitle;

        // Set notification properties based on task status
        switch(material.taskStatus) {
          case 'under_review':
            relevantDate = material.reviewStartDate || material.submissionDate || material.postedDate;
            relevantTime = material.reviewStartTime || material.submissionTime || material.postedTime;
            notificationTitle = `Your submission for "${material.title}" is under review`;
            break;
          case 'reviewed':
            relevantDate = material.reviewedDate || material.submissionDate || material.postedDate;
            relevantTime = material.reviewedTime || material.submissionTime || material.postedTime;
            notificationTitle = `Your submission for "${material.title}" has been reviewed`;
            break;
          case 'submitted':
            relevantDate = material.submissionDate || material.postedDate;
            relevantTime = material.submissionTime || material.postedTime;
            notificationTitle = `You've submitted "${material.title}"`;
            break;
          case 'past_due':
            relevantDate = material.dueDate || material.postedDate;
            relevantTime = material.dueTime || material.postedTime;
            notificationTitle = `Assignment "${material.title}" is past due`;
            break;
          case 'pending':
            relevantDate = material.postedDate;
            relevantTime = material.postedTime;
            notificationTitle = `Assignment: ${material.title}`;
            break;
          case 'turned_in':
            relevantDate = material.submissionDate || material.postedDate;
            relevantTime = material.submissionTime || material.postedTime;
            notificationTitle = `You've completed "${material.title}"`;
            break;
          default:
            relevantDate = material.postedDate;
            relevantTime = material.postedTime;
            notificationTitle = `Assignment: ${material.title}`;
        }

        const notificationDate = new Date(`${relevantDate}T${relevantTime || '00:00'}`);
        let timeLabel;

        // Format the time label based on when the event occurred
        if (isToday(notificationDate)) {
          // If it happened today, show the time
          timeLabel = formatTime(relevantTime);
        } else if (isYesterday(notificationDate)) {
          // If it happened yesterday, show "Yesterday"
          timeLabel = yesterdayStr;
        } else {
          // Otherwise, show the date
          timeLabel = formatDate(relevantDate);
        }

        // Create notification with more detailed information
        newNotifications.push({
          id: material.id,
          classId: classItem.id,
          materialTitle: material.title,
          className: classItem.title,
          title: notificationTitle,
          time: timeLabel,
          read: false,
          date: relevantDate,
          dueDate: material.dueDate,
          dueTime: material.dueTime,
          timestamp: notificationDate.getTime(),
          taskStatus: material.taskStatus || 'pending',
          // Add more details for better context
          taskDescription: material.taskDescription || '',
          isComplete: material.isComplete || false,
          submissionDate: material.submissionDate,
          submissionTime: material.submissionTime,
          reviewStartDate: material.reviewStartDate,
          reviewStartTime: material.reviewStartTime,
          reviewedDate: material.reviewedDate,
          reviewedTime: material.reviewedTime,
          taskScore: material.taskScore
        });
      }
    });
  });

  // Get stored notifications to preserve read status
  const storedNotifications = localStorage.getItem(NOTIFICATION_STORAGE_KEY);
  let existingNotifications = [];

  if (storedNotifications) {
    existingNotifications = JSON.parse(storedNotifications);

    // Update read status for existing notifications
    newNotifications.forEach(newNotif => {
      const existingNotif = existingNotifications.find(
        existing => existing.id === newNotif.id &&
                    existing.classId === newNotif.classId &&
                    existing.taskStatus === newNotif.taskStatus
      );

      if (existingNotif && existingNotif.read) {
        newNotif.read = true;
      }
    });
  }

  // Filter out cleared notifications
  const filteredNotifications = newNotifications.filter(notification => {
    return !isNotificationCleared(notification);
  });
  // Sort notifications: prioritize read status, then by timestamp
  filteredNotifications.sort((a, b) => {
    // First sort by read status (unread first)
    if (a.read !== b.read) {
      return a.read ? 1 : -1;
    }
    // Then by timestamp (newest first) for notifications with same read status
    return b.timestamp - a.timestamp;
  });

  // Take only the most recent notifications (limit to 15 for more context)
  notifications.value = filteredNotifications.slice(0, 15);

  // Save to localStorage
  saveNotifications();
};

// Save notifications to localStorage
const saveNotifications = () => {
  localStorage.setItem(NOTIFICATION_STORAGE_KEY, JSON.stringify(notifications.value));
};

// Save cleared notifications to localStorage
const saveClearedNotifications = () => {
  localStorage.setItem(CLEARED_NOTIFICATIONS_KEY, JSON.stringify(clearedNotifications.value));
};

// Helper function to create a unique identifier for a notification
const getNotificationIdentifier = (notification) => {
  // Create a more robust identifier that includes all relevant properties
  // This ensures that notifications are uniquely identified even if they have
  // the same id, classId, and taskStatus but different content
  return `${notification.id}-${notification.classId}-${notification.taskStatus}`;
};

// Check if a notification has been cleared
const isNotificationCleared = (notification) => {
  return clearedNotifications.value.includes(getNotificationIdentifier(notification));
};

// Toggle notifications dropdown
const toggleNotifications = (event) => {
  // Prevent event propagation to avoid triggering handleClickOutside
  if (event) {
    event.stopPropagation();
  }

  // Toggle dropdown visibility
  showNotifications.value = !showNotifications.value;

  // Log for debugging
  console.log('Notification dropdown toggled:', showNotifications.value);
};

// Handle click outside to close dropdowns
const handleClickOutside = (event) => {
  // Check if click is outside notification dropdown and notification bell
  if (showNotifications.value &&
      notificationDropdown.value &&
      !notificationDropdown.value.contains(event.target) &&
      !event.target.closest('.notification-bell') &&
      !event.target.closest('button[class*="notification"]')) {
    showNotifications.value = false;
  }

  // Check if click is outside profile dropdown
  if (showProfileDropdown.value &&
      profileDropdown.value &&
      !profileDropdown.value.contains(event.target) &&
      !event.target.closest('button[aria-label="User profile"]')) {
    showProfileDropdown.value = false;
  }
};

// Toggle profile dropdown
const toggleProfileDropdown = (event) => {
  // Prevent event propagation to avoid triggering handleClickOutside
  if (event) {
    event.stopPropagation();
  }

  // Toggle dropdown visibility
  showProfileDropdown.value = !showProfileDropdown.value;

  // Close notifications dropdown if open
  if (showProfileDropdown.value && showNotifications.value) {
    showNotifications.value = false;
  }
};

// Logout function
const logout = () => {
  // Close the dropdown
  showProfileDropdown.value = false;

  // Clear relevant localStorage items
  localStorage.removeItem(STORAGE_KEYS.CLASSES);
  localStorage.removeItem(STORAGE_KEYS.CURRENT_CLASS_ID);
  localStorage.removeItem(STORAGE_KEYS.CURRENT_MATERIAL_ID);
  localStorage.removeItem(STORAGE_KEYS.CLASS_SOURCE);
  localStorage.removeItem(STORAGE_KEYS.ACTIVE_TAB);
  localStorage.removeItem(NOTIFICATION_STORAGE_KEY);
  localStorage.removeItem(CLEARED_NOTIFICATIONS_KEY);

  // Redirect to login page
  router.push('/login');
};

// Helper functions for notification display
const formatDueDate = (notification) => {
  if (!notification.dueDate) return '';

  const dueDate = new Date(`${notification.dueDate}T${notification.dueTime || '23:59'}`);
  const now = new Date();
  const tomorrow = new Date(now);
  tomorrow.setDate(tomorrow.getDate() + 1);

  // If due today
  if (dueDate.toDateString() === now.toDateString()) {
    return `Today, ${formatTime(notification.dueTime)}`;
  }

  // If due tomorrow
  if (dueDate.toDateString() === tomorrow.toDateString()) {
    return `Tomorrow, ${formatTime(notification.dueTime)}`;
  }

  // Otherwise, show full date
  return `${formatDate(notification.dueDate)}, ${formatTime(notification.dueTime)}`;
};

// Check if a task is due soon (within 2 days)
const isDueSoon = (notification) => {
  if (!notification.dueDate) return false;

  const dueDate = new Date(`${notification.dueDate}T${notification.dueTime || '23:59'}`);
  const now = new Date();

  // Calculate difference in days
  const diffTime = dueDate.getTime() - now.getTime();
  const diffDays = diffTime / (1000 * 3600 * 24);

  // Return true if due within 2 days or past due
  return diffDays < 2;
};

// Get status color class for badges - using the same styling as in studentUtils.js
const getStatusColorClass = (status) => {
  switch (status) {
    case 'pending':
      return 'bg-yellow-50 text-yellow-600 border border-yellow-100 shadow-sm';
    case 'past_due':
      return 'bg-red-50 text-red-600 border border-red-100 shadow-sm';
    case 'submitted':
      return 'bg-blue-50 text-blue-600 border border-blue-100 shadow-sm';
    case 'under_review':
      return 'bg-orange-50 text-orange-600 border border-orange-100 shadow-sm';
    case 'reviewed':
      return 'bg-green-50 text-green-600 border border-green-100 shadow-sm';
    case 'turned_in':
      return 'bg-green-50 text-green-600 border border-green-100 shadow-sm';
    default:
      return 'bg-gray-50 text-gray-600 border border-gray-100 shadow-sm';
  }
};

// Format task status for shorter display
const formatTaskStatusShort = (status) => {
  switch (status) {
    case 'submitted':
      return 'Submitted';
    case 'under_review':
      return 'Under Review';
    case 'reviewed':
      return 'Reviewed';
    case 'turned_in':
      return 'Completed';
    case 'past_due':
      return 'Past Due';
    default:
      return 'Pending';
  }
};

// Computed property for unread notifications count
const unreadNotificationsCount = computed(() => {
  return notifications.value.filter(notification => !notification.read).length;
});

// Computed property for unread notifications
const unreadNotifications = computed(() => {
  return notifications.value.filter(notification => !notification.read);
});

// Computed property for read notifications
const readNotifications = computed(() => {
  return notifications.value.filter(notification => notification.read);
});

// Computed properties to check active pages
const isHomeActive = computed(() => {
  return route.path === '/student/dashboard';
});

const isAcademyActive = computed(() => {
  // Only highlight Academy when on academy or class detail pages, but not on classes page
  return route.path === '/student/academy' ||
         (route.path.startsWith('/student/class/') && route.path !== '/student/classes');
});

const isClassesActive = computed(() => {
  // Highlight Classes when on the classes page or class detail page
  return route.path === '/student/classes' || route.path.includes('/student/class-detail/');
});

// Handle hover leave with a small delay to improve user experience
const handleHoverLeave = () => {
  // Only hide the toggle button if the sidebar is closed
  if (!isSidebarOpen.value) {
    // Add a small delay before hiding the toggle button
    // This prevents the button from disappearing too quickly
    setTimeout(() => {
      // Double-check sidebar is still closed before hiding
      if (!isSidebarOpen.value) {
        showHoverToggle.value = false;
      }
    }, 300); // Reduced delay for better responsiveness
  }
};

// Helper function to reset navigation state without forcing page reload
const resetNavigationState = () => {
  // Clear navigation state from localStorage
  localStorage.removeItem(STORAGE_KEYS.CLASS_SOURCE);
  localStorage.removeItem(STORAGE_KEYS.CURRENT_CLASS_ID);
  console.log('Reset navigation state from localStorage');
};

onMounted(() => {
  window.addEventListener('click', handleClickOutside);
  window.addEventListener('keydown', handleKeyDown);

  // First load cleared notifications to ensure they're available
  // before generating any new notifications
  loadClearedNotifications();

  // Then initialize notifications
  loadNotifications();

  // Load user profile data if available
  try {
    const userData = localStorage.getItem('user_profile');
    if (userData) {
      const profile = JSON.parse(userData);
      if (profile.fullName) {
        userName.value = profile.fullName;
      }
    }
  } catch (error) {
    console.error('Error loading user profile:', error);
  }

  // Debug current route and active states
  console.log('Current route path:', route.path);
  console.log('isHomeActive:', isHomeActive.value);
  console.log('isAcademyActive:', isAcademyActive.value);
  console.log('isClassesActive:', isClassesActive.value);
  console.log('Cleared notifications count:', clearedNotifications.value.length);
});

onUnmounted(() => {
  window.removeEventListener('click', handleClickOutside);
  window.removeEventListener('keydown', handleKeyDown);
});

// Watch for route changes to debug active states
watch(() => route.path, (newPath) => {
  console.log('=== NAVIGATION DEBUG ===');
  console.log('Route changed to:', newPath);
  console.log('Route query params:', route.query);
  console.log('isHomeActive:', isHomeActive.value);
  console.log('isAcademyActive:', isAcademyActive.value);
  console.log('isClassesActive:', isClassesActive.value);
  console.log('localStorage classSource:', localStorage.getItem(STORAGE_KEYS.CLASS_SOURCE));
  console.log('localStorage currentClassId:', localStorage.getItem(STORAGE_KEYS.CURRENT_CLASS_ID));
});

// Close notifications dropdown when sidebar is toggled
watch(isSidebarOpen, (newValue) => {
  if (newValue && showNotifications.value) {
    showNotifications.value = false;
  }
});

// Watch for changes in class data to update notifications
watch(() => classStore.classes.value, (newClasses, oldClasses) => {
  console.log('Class data changed, checking for new notifications...');

  // Check for new materials or status changes that might need to be shown
  // even if similar notifications were previously cleared
  if (oldClasses && newClasses) {
    newClasses.forEach((newClass, classIndex) => {
      // Handle new classes that don't exist in oldClasses
      if (!oldClasses[classIndex]) {
        console.log(`New class detected: ${newClass.id} - ${newClass.title}`);

        // For new classes, ensure all materials with tasks generate notifications
        newClass.materials.forEach(material => {
          if (material.hasTask) {
            // Create a mock notification to get its identifier
            const mockNotification = {
              id: material.id,
              classId: newClass.id,
              taskStatus: material.taskStatus || 'pending'
            };

            // Get the identifier
            const identifier = getNotificationIdentifier(mockNotification);

            // Remove from cleared list to ensure it appears
            const identifierIndex = clearedNotifications.value.indexOf(identifier);
            if (identifierIndex !== -1) {
              clearedNotifications.value.splice(identifierIndex, 1);
              saveClearedNotifications();
              console.log(`Removed from cleared list for new class material: ${identifier}`);
            }
          }
        });
      }
      // Handle existing classes
      else {
        // Check each material in the class
        newClass.materials.forEach((newMaterial, materialIndex) => {
          // Handle new materials that don't exist in oldClass
          if (materialIndex >= oldClasses[classIndex].materials.length) {
            console.log(`New material detected: ${newMaterial.id} in class ${newClass.id}`);

            // For new materials with tasks, ensure they generate notifications
            if (newMaterial.hasTask) {
              // Create a mock notification to get its identifier
              const mockNotification = {
                id: newMaterial.id,
                classId: newClass.id,
                taskStatus: newMaterial.taskStatus || 'pending'
              };

              // Get the identifier
              const identifier = getNotificationIdentifier(mockNotification);

              // Remove from cleared list to ensure it appears
              const identifierIndex = clearedNotifications.value.indexOf(identifier);
              if (identifierIndex !== -1) {
                clearedNotifications.value.splice(identifierIndex, 1);
                saveClearedNotifications();
                console.log(`Removed from cleared list for new material: ${identifier}`);
              }
            }
          }
          // Handle existing materials with changed task status
          else {
            const oldMaterial = oldClasses[classIndex].materials[materialIndex];

            // If task status has changed
            if (newMaterial.taskStatus !== oldMaterial.taskStatus) {
              console.log(`Task status changed for material ${newMaterial.id}: ${oldMaterial.taskStatus} -> ${newMaterial.taskStatus}`);

              // Create a mock notification for the new status
              const mockNotification = {
                id: newMaterial.id,
                classId: newClass.id,
                taskStatus: newMaterial.taskStatus
              };

              // Get the identifier
              const identifier = getNotificationIdentifier(mockNotification);

              // Remove from cleared list to ensure it appears
              const identifierIndex = clearedNotifications.value.indexOf(identifier);
              if (identifierIndex !== -1) {
                clearedNotifications.value.splice(identifierIndex, 1);
                saveClearedNotifications();
                console.log(`Removed from cleared list due to status change: ${identifier}`);
              }
            }
          }
        });
      }
    });
  }

  // Regenerate notifications when class data changes
  generateNotificationsFromClasses();
  console.log('Notifications updated due to class data change');
}, { deep: true });

// Watch for changes in current material's task status
watch(() => classStore.currentMaterial.value?.taskStatus, (newStatus, oldStatus) => {
  if (newStatus !== oldStatus && newStatus && oldStatus) {
    // Get the current material and class
    const currentMaterial = classStore.currentMaterial.value;
    const currentClass = classStore.currentClass.value;

    if (currentMaterial && currentClass) {
      console.log(`Task status changed: ${oldStatus} -> ${newStatus} for material ${currentMaterial.id} in class ${currentClass.id}`);

      // Create a mock notification object for the old status to get its identifier
      const oldNotification = {
        id: currentMaterial.id,
        classId: currentClass.id,
        taskStatus: oldStatus
      };

      // Create a mock notification object for the new status to get its identifier
      const newNotification = {
        id: currentMaterial.id,
        classId: currentClass.id,
        taskStatus: newStatus
      };

      // Get identifiers for both old and new status notifications
      const oldIdentifier = getNotificationIdentifier(oldNotification);
      const newIdentifier = getNotificationIdentifier(newNotification);

      // Always remove the new status notification from cleared list
      // This ensures that when a task status changes to a new state, it will always appear
      const newIdentifierIndex = clearedNotifications.value.indexOf(newIdentifier);

      if (newIdentifierIndex !== -1) {
        clearedNotifications.value.splice(newIdentifierIndex, 1);
        saveClearedNotifications();
        console.log(`Removed new notification from cleared list: ${newIdentifier}`);
      }

      // For specific status transitions that should always generate a notification
      // (like when a submission is reviewed or under review), also remove from cleared list
      if (newStatus === 'under_review' || newStatus === 'reviewed') {
        // These are important status changes that should always generate a notification
        // even if the user previously cleared similar notifications
        console.log(`Important status change detected: ${newStatus}. Ensuring notification will appear.`);

        // If the old status notification was in the cleared list, remove it too
        // This helps maintain a clean cleared list
        const oldIdentifierIndex = clearedNotifications.value.indexOf(oldIdentifier);
        if (oldIdentifierIndex !== -1) {
          clearedNotifications.value.splice(oldIdentifierIndex, 1);
          saveClearedNotifications();
          console.log(`Removed old notification from cleared list: ${oldIdentifier}`);
        }
      }
    }

    // Regenerate notifications when task status changes
    generateNotificationsFromClasses();
    console.log(`Notifications updated due to task status change: ${oldStatus} -> ${newStatus}`);

    // If the new status is 'under_review' or 'reviewed', mark the notification as unread
    if (newStatus === 'under_review' || newStatus === 'reviewed') {
      if (currentMaterial && currentClass) {
        // Find the notification for this material
        const notificationIndex = notifications.value.findIndex(n =>
          n.id === currentMaterial.id &&
          n.classId === currentClass.id &&
          n.taskStatus === newStatus
        );

        // If found, mark as unread to highlight the status change
        if (notificationIndex !== -1) {
          notifications.value[notificationIndex].read = false;

          // Re-sort notifications after marking as unread
          resortNotifications();

          // Save to localStorage
          saveNotifications();
        }
      }
    }
  }
});

// Watch for sidebar state changes to reset hover toggle state
watch(isSidebarOpen, (newValue, oldValue) => {
  // If sidebar was open and is now closed, reset the hover toggle state
  if (oldValue === true && newValue === false) {
    // Reset hover toggle state immediately
    showHoverToggle.value = false;
    console.log('Sidebar closed, hover toggle state reset');
  }

  // Close profile dropdown when sidebar is toggled
  if (showProfileDropdown.value) {
    showProfileDropdown.value = false;
  }
});

// Close profile dropdown when route changes
watch(() => route.path, () => {
  if (showProfileDropdown.value) {
    showProfileDropdown.value = false;
  }
});

// Close both dropdowns when ESC key is pressed
const handleKeyDown = (event) => {
  if (event.key === 'Escape') {
    if (showNotifications.value) {
      showNotifications.value = false;
    }
    if (showProfileDropdown.value) {
      showProfileDropdown.value = false;
    }
  }
};

// Mark all notifications as read
const markAllAsRead = (event) => {
  if (event) {
    event.stopPropagation();
  }

  // Mark all notifications as read
  notifications.value.forEach(notification => {
    notification.read = true;
  });

  // Re-sort notifications after marking all as read
  // This is needed because we sort by read status
  resortNotifications();

  // Save to localStorage
  saveNotifications();

  console.log('All notifications marked as read');
};

// Resort notifications based on read status and timestamp
const resortNotifications = () => {
  notifications.value.sort((a, b) => {
    // First sort by read status (unread first)
    if (a.read !== b.read) {
      return a.read ? 1 : -1;
    }
    // Then by timestamp (newest first) for notifications with same read status
    return b.timestamp - a.timestamp;
  });
};

// Clear a single notification
const clearSingleNotification = (notification, index) => {
  // Check if notification is already cleared
  if (!isNotificationCleared(notification)) {
    // Add the notification identifier to the cleared list
    const identifier = getNotificationIdentifier(notification);
    clearedNotifications.value.push(identifier);

    // Save cleared notifications to localStorage
    saveClearedNotifications();

    console.log(`Notification added to cleared list: ${identifier}`);
  }

  // Remove the notification from the current view
  notifications.value.splice(index, 1);

  // Save to localStorage
  saveNotifications();

  console.log(`Notification removed from current view`);
};

// Clear all notifications
const clearAllNotifications = (event) => {
  if (event) {
    event.stopPropagation();
  }

  // Store the identifiers of all current notifications in the cleared list
  let newClearedCount = 0;

  notifications.value.forEach(notification => {
    // Check if notification is already cleared
    if (!isNotificationCleared(notification)) {
      // Add the notification identifier to the cleared list
      const identifier = getNotificationIdentifier(notification);
      clearedNotifications.value.push(identifier);
      newClearedCount++;
    }
  });

  // Save cleared notifications to localStorage if any new ones were added
  if (newClearedCount > 0) {
    saveClearedNotifications();
    console.log(`Added ${newClearedCount} notifications to cleared list`);
  }

  // Clear all notifications from the current view
  notifications.value = [];

  // Save to localStorage
  saveNotifications();

  // Close dropdown after clearing
  showNotifications.value = false;

  console.log('All notifications cleared from view');
};

// Navigate to task detail when notification is clicked
const navigateToNotification = (notification) => {
  // Set current class and material
  classStore.setCurrentClass(notification.classId);
  classStore.setCurrentMaterial(notification.id);

  // Close notification dropdown
  showNotifications.value = false;

  // Mark notification as read
  const notificationIndex = notifications.value.findIndex(n =>
    n.id === notification.id &&
    n.classId === notification.classId &&
    n.taskStatus === notification.taskStatus
  );

  if (notificationIndex !== -1) {
    // Mark the notification as read
    notifications.value[notificationIndex].read = true;

    // Move the notification to the bottom of the list by re-sorting
    // This will automatically place read notifications at the bottom
    // while maintaining the timestamp order within each category
    resortNotifications();

    // Save to localStorage
    saveNotifications();
  }

  // Determine appropriate navigation based on task status
  switch (notification.taskStatus) {
    case 'pending':
    case 'past_due':
      // For tasks that need action, go to task detail
      router.push({
        name: 'DetailTaskHistory',
        params: {
          classId: notification.classId,
          materialId: notification.id
        },
        query: {
          source: 'notification',
          action: 'submit'
        }
      });
      break;

    case 'submitted':
    case 'under_review':
      // For tasks that are in progress, go to task detail
      router.push({
        name: 'DetailTaskHistory',
        params: {
          classId: notification.classId,
          materialId: notification.id
        },
        query: {
          source: 'notification',
          action: 'view'
        }
      });
      break;

    case 'reviewed':
      // For reviewed tasks, go to task detail with view grades action
      router.push({
        name: 'DetailTaskHistory',
        params: {
          classId: notification.classId,
          materialId: notification.id
        },
        query: {
          source: 'notification',
          action: 'viewGrades'
        }
      });
      break;

    case 'turned_in':
      // For completed tasks, go to task detail
      router.push({
        name: 'DetailTaskHistory',
        params: {
          classId: notification.classId,
          materialId: notification.id
        },
        query: {
          source: 'notification',
          action: 'view'
        }
      });
      break;

    default:
      // If the material is complete but not a task, go to learning material detail
      if (notification.isComplete) {
        router.push({
          name: 'DetailLearningMaterials',
          params: {
            classId: notification.classId,
            materialId: notification.id
          },
          query: {
            source: 'notification'
          }
        });
      } else {
        // Default fallback to class detail
        router.push({
          name: 'DetailClass',
          params: {
            classId: notification.classId
          },
          query: {
            source: 'notification'
          }
        });
      }
  }

  console.log('Navigating to notification:', notification);
};
</script>

<script>
export default {
  name: 'NavbarStudent'
}
</script>


