export const programTypes = [
  { id: 'backend', name: 'Back End Development', icon: '/icons/server.svg' },
  { id: 'frontend', name: 'Front End Development', icon: '/icons/laptop.svg' },
  { id: 'mobile', name: 'Mobile App Development', icon: '/icons/phone.svg' }
];

export const mentors = [
  {
    id: 1,
    name: '<PERSON>',
    role: 'Senior Backend Engineer',
    image: '/theresa.png',
    bio: 'Experienced backend developer with 8+ years in enterprise development. Specializes in building scalable microservices and RESTful APIs with TypeScript, Node.js, and Nest.js.',
    socialLinks: { twitter: '#', linkedin: '#' },
    specialties: ['Nest.js', 'Node.js', 'TypeScript', 'Microservices']
  },
  {
    id: 2,
    name: '<PERSON>',
    role: 'Systems Architect',
    image: '/courtney.png',
    bio: 'Golang expert with experience at major tech companies including Google and Uber. Passionate about teaching concurrent programming and building high-performance distributed systems.',
    socialLinks: { twitter: '#', linkedin: '#' },
    specialties: ['Golang', 'Distributed Systems', 'Concurrency', 'Cloud Architecture']
  },
  {
    id: 3,
    name: '<PERSON>',
    role: 'Frontend Architect',
    image: '/albert.png',
    bio: 'Frontend specialist with 6+ years of modern web development experience. Expert in React ecosystem, Next.js, server-side rendering, and performance optimization.',
    socialLinks: { twitter: '#', linkedin: '#' },
    specialties: ['Next.js', 'React.js', 'SSR', 'Performance Optimization']
  },
  {
    id: 4,
    name: 'Marvin McKinney',
    role: 'Senior React Developer',
    image: '/marvin.png',
    bio: 'React.js developer with 7+ years of experience building modern web applications. Specializes in state management, hooks, component architecture, and testing.',
    socialLinks: { twitter: '#', linkedin: '#' },
    specialties: ['React.js', 'Redux', 'Testing', 'Component Architecture']
  },
  {
    id: 5,
    name: 'Jessica Parker',
    role: 'Vue.js Specialist',
    image: '/sarah.png',
    bio: 'Vue.js expert with 5+ years of experience in progressive web applications. Passionate about component-based architecture, Vuex state management, and modern UI development.',
    socialLinks: { twitter: '#', linkedin: '#' },
    specialties: ['Vue.js', 'Vuex', 'Progressive Web Apps', 'UI/UX']
  },
  {
    id: 6,
    name: 'Daniel Wilson',
    role: 'Enterprise Frontend Lead',
    image: '/johncooper.png',
    bio: 'Angular developer with 6+ years of experience building enterprise applications. Expert in TypeScript, RxJS, NgRx for state management, and large-scale application architecture.',
    socialLinks: { twitter: '#', linkedin: '#' },
    specialties: ['Angular', 'TypeScript', 'RxJS', 'Enterprise Architecture']
  },
  {
    id: 7,
    name: 'Sophia Martinez',
    role: 'Mobile Development Expert',
    image: '/maria.png',
    bio: 'Flutter developer with expertise in cross-platform mobile app development. Specializes in creating beautiful, responsive UIs with Dart and native platform integration.',
    socialLinks: { twitter: '#', linkedin: '#' },
    specialties: ['Flutter', 'Dart', 'Mobile UI/UX', 'Cross-platform Development']
  }
];

export const programs = [
  {
    id: 1,
    type: 'frontend',
    title: 'Next Js',
    description: 'Build modern React applications with server-side rendering, static site generation, and more.',
    mentors: [
      {
        id: 1,
        name: 'Patricia Vero',
        image: '/patricia.png',
        price: 750000,
        duration: '5 Weeks',
        role: 'Senior Next.js Developer',
        company: 'Vercel',
        bio: 'Patricia is a seasoned Next.js developer with extensive experience building scalable web applications. She specializes in server-side rendering, static site generation, and performance optimization techniques.'
      },
      {
        id: 2,
        name: 'Albert Flores',
        image: '/albert.png',
        price: 680000,
        duration: '4 Weeks',
        role: 'Lead Frontend Engineer',
        company: 'Meta',
        bio: 'Albert has worked on numerous high-traffic websites using Next.js and React. He focuses on performance optimization, modern frontend architecture, and scalable application design.'
      },
      {
        id: 3,
        name: 'Theresa Webb',
        image: '/theresa.png',
        price: 820000,
        duration: '6 Weeks',
        role: 'Full-Stack Developer',
        company: 'Vercel',
        bio: 'Theresa brings a comprehensive approach to Next.js development, with expertise in both frontend and backend integration. Her courses cover the entire development lifecycle from design to deployment.'
      }
    ]
  },
  {
    id: 2,
    type: 'frontend',
    title: 'React Js',
    description: 'Master React and build interactive user interfaces with the most popular front-end library.',
    mentors: [
      {
        id: 1,
        name: 'Marvin McKinney',
        image: '/marvin.png',
        price: 650000,
        duration: '4 Weeks',
        role: 'Senior React Developer',
        company: 'Meta',
        bio: 'Marvin has been working with React since its early days and has contributed to several open-source React libraries. He specializes in state management, hooks, and component architecture patterns.'
      },
      {
        id: 2,
        name: 'Courtney Henry',
        image: '/courtney.png',
        price: 580000,
        duration: '3 Weeks',
        role: 'UI/UX Engineer',
        company: 'Airbnb',
        bio: 'Courtney combines design expertise with React development skills to create beautiful, functional user interfaces. Her approach focuses on user experience, accessibility, and modern design systems.'
      }
    ]
  },
  {
    id: 3,
    type: 'frontend',
    title: 'Vue Js',
    description: 'Learn to build dynamic single-page applications with Vue.js, a progressive JavaScript framework.',
    mentors: [
      {
        id: 1,
        name: 'Jessica Parker',
        image: '/sarah.png',
        price: 720000,
        duration: '5 Weeks',
        role: 'Senior Vue.js Developer',
        company: 'GitLab',
        bio: 'Jessica has extensive experience with Vue.js ecosystem and has built numerous production applications. She specializes in state management with Vuex/Pinia and single-page application architecture.'
      },
      {
        id: 2,
        name: 'John Smith',
        image: '/johncooper.png',
        price: 670000,
        duration: '4 Weeks',
        role: 'Frontend Architect',
        company: 'Netlify',
        bio: 'John focuses on building high-performance Vue.js applications with an emphasis on progressive web app capabilities, optimization techniques, and modern development workflows.'
      }
    ]
  },
  {
    id: 4,
    type: 'backend',
    title: 'Golang',
    description: "Master Google's programming language to build fast, reliable, and efficient back-end systems.",
    mentors: [
      {
        id: 1,
        name: 'Cindy Cloe',
        image: '/courtney.png',
        price: 500000,
        duration: '3 Weeks',
        role: 'Senior Go Developer',
        company: 'Google',
        bio: 'Cindy specializes in building high-performance microservices with Go. Her expertise includes concurrency patterns and efficient system design.'
      },
      {
        id: 2,
        name: 'Michael Brown',
        image: '/marvin.png',
        price: 550000,
        duration: '4 Weeks',
        role: 'Backend Systems Engineer',
        company: 'Cloudflare',
        bio: 'Michael has extensive experience building large-scale distributed systems with Go. He focuses on performance optimization and scalable architecture.'
      }
    ]
  },
  {
    id: 5,
    type: 'backend',
    title: 'Node.js',
    description: 'Learn server-side JavaScript programming with Node.js to build scalable network applications.',
    mentors: [
      {
        id: 1,
        name: 'Cindy Cloe',
        image: '/courtney.png',
        price: 500000,
        duration: '3 Weeks',
        role: 'Senior Node.js Developer',
        company: 'Microsoft',
        bio: 'Cindy has built numerous production-grade Node.js applications and APIs. She specializes in Express.js and RESTful API design.'
      },
      {
        id: 2,
        name: 'David Wilson',
        image: '/albert.png',
        price: 550000,
        duration: '4 Weeks',
        role: 'Full-Stack JavaScript Developer',
        company: 'Stripe',
        bio: 'David specializes in full-stack JavaScript development with a focus on Node.js backends. He has extensive experience with MongoDB and real-time application architecture.'
      }
    ]
  },
  {
    id: 6,
    type: 'backend',
    title: 'Nest Js',
    description: 'Build scalable and maintainable server-side applications using the power of TypeScript and Node.js.',
    mentors: [
      {
        id: 1,
        name: 'Cindy Cloe',
        image: '/courtney.png',
        price: 500000,
        duration: '3 Weeks',
        role: 'Senior Nest.js Developer',
        company: 'Google',
        bio: 'Cindy specializes in building enterprise-grade applications with Nest.js. Her expertise includes TypeScript, dependency injection, and microservice architecture.'
      },
      {
        id: 2,
        name: 'Robert Johnson',
        image: '/marvin.png',
        price: 600000,
        duration: '5 Weeks',
        role: 'Backend Architect',
        company: 'Microsoft',
        bio: 'Robert has extensive experience designing and implementing large-scale backend systems with Nest.js. He focuses on scalable architecture and best practices.'
      }
    ]
  },
  {
    id: 7,
    type: 'frontend',
    title: 'Angular',
    description: 'Master Angular to build dynamic, single-page web applications with TypeScript.',
    mentors: [
      {
        id: 1,
        name: 'Patricia Vero',
        image: '/theresa.png',
        price: 700000,
        duration: '5 Weeks',
        role: 'Senior Angular Developer',
        company: 'Google',
        bio: 'Patricia has been working with Angular since its early versions and has built numerous enterprise applications. She specializes in reactive programming with RxJS and TypeScript.'
      },
      {
        id: 2,
        name: 'Sarah Miller',
        image: '/courtney.png',
        price: 650000,
        duration: '4 Weeks',
        role: 'Front-End Architect',
        company: 'Microsoft',
        bio: 'Sarah specializes in building large-scale Angular applications with a focus on state management using NgRx and enterprise architecture patterns.'
      }
    ]
  },
  {
    id: 8,
    type: 'frontend',
    title: 'Tailwind CSS',
    description: 'Learn to rapidly build modern websites without ever leaving your HTML with this utility-first CSS framework.',
    mentors: [
      {
        id: 1,
        name: 'Patricia Vero',
        image: '/theresa.png',
        price: 700000,
        duration: '5 Weeks',
        role: 'Senior UI Developer',
        company: 'Tailwind Labs',
        bio: 'Patricia is an expert in building modern user interfaces with Tailwind CSS. She specializes in responsive design and creating reusable component systems.'
      },
      {
        id: 2,
        name: 'James Taylor',
        image: '/albert.png',
        price: 600000,
        duration: '3 Weeks',
        role: 'UI/UX Designer & Developer',
        company: 'Figma',
        bio: 'James combines design expertise with development skills to create beautiful interfaces with Tailwind CSS. He focuses on design systems and component libraries.'
      }
    ]
  },
  {
    id: 9,
    type: 'frontend',
    title: 'TypeScript',
    description: 'Add static typing to JavaScript to improve developer productivity and code quality.',
    mentors: [
      {
        id: 1,
        name: 'Patricia Vero',
        image: '/theresa.png',
        price: 700000,
        duration: '5 Weeks',
        role: 'Senior TypeScript Developer',
        company: 'Microsoft',
        bio: 'Patricia has extensive experience with TypeScript in large-scale applications. She specializes in advanced type systems and integration with modern JavaScript frameworks.'
      },
      {
        id: 2,
        name: 'Emily Davis',
        image: '/courtney.png',
        price: 750000,
        duration: '6 Weeks',
        role: 'Software Architect',
        company: 'GitHub',
        bio: 'Emily focuses on enterprise-grade TypeScript applications with an emphasis on architecture, design patterns, and code quality. Her courses cover advanced TypeScript concepts.'
      }
    ]
  },
];

export const testimonials = [
  {
    id: 1,
    name: 'Sarah Kristen',
    role: 'Student, National University',
    image: '/sarah.png',
    text: 'The Back-End Development Bootcamp was a game-changer for me! The mentors were incredibly supportive, and I learned Nest JS and Golang from scratch.',
    rating: 5,
    program: 'Back-End Development'
  },
  {
    id: 2,
    name: 'John Cooper',
    role: 'Student, Tech Institute',
    image: '/johncooper.png',
    text: 'Front-End bootcamp helped me transition from zero coding knowledge to a confident React developer. Amazing experience!',
    rating: 5,
    program: 'Front-End Development'
  },
  {
    id: 3,
    name: 'Maria Garcia',
    role: 'Student, Digital Academy',
    image: '/maria.png',
    text: 'The Mobile Development program exceeded my expectations. Now I can build professional Flutter applications with confidence.',
    rating: 5,
    program: 'Mobile Development'
  }
];

export const freeClasses = [
  {
    id: 1,
    title: 'Intro To Next JS: Build Modern Web Apps',
    date: '15/01/2025',
    time: '2:00 PM - 4:00 PM',
    image: '/class1.png',
    type: 'frontend',
    mentorImage: '/theresa.png'
  },
  {
    id: 2,
    title: 'REST API With Golang: Create Scalable Services',
    date: '18/01/2025',
    time: '10:00 AM - 12:00 PM',
    image: '/class2.png',
    type: 'backend',
    mentorImage: '/marvin.png'
  },
  {
    id: 3,
    title: 'Mobile App Development With Flutter',
    date: '22/01/2025',
    time: '3:00 PM - 5:00 PM',
    image: '/class3.png',
    type: 'mobile',
    mentorImage: '/maria.png'
  },
  {
    id: 4,
    title: 'Advanced Next JS Techniques',
    date: '25/01/2025',
    time: '1:00 PM - 3:00 PM',
    image: '/class4.png',
    type: 'frontend',
    mentorImage: '/albert.png'
  },
  {
    id: 5,
    title: 'Building RESTful APIs with Go',
    date: '29/01/2025',
    time: '11:00 AM - 1:00 PM',
    image: '/class5.png',
    type: 'backend',
    mentorImage: '/courtney.png'
  },
  {
    id: 6,
    title: 'Flutter UI Design Fundamentals',
    date: '01/02/2025',
    time: '2:30 PM - 4:30 PM',
    image: '/class6.png',
    type: 'mobile',
    mentorImage: '/marvin.png'
  },
  {
    id: 7,
    title: 'Optimizing Next JS Performance',
    date: '05/02/2025',
    time: '4:00 PM - 6:00 PM',
    image: '/class7.png',
    type: 'frontend',
    mentorImage: '/courtney.png'
  },
  {
    id: 8,
    title: 'Go Microservices Workshop',
    date: '08/02/2025',
    time: '9:00 AM - 11:00 AM',
    image: '/class8.png',
    type: 'backend',
    mentorImage: '/albert.png'
  },
  {
    id: 9,
    title: 'State Management in Flutter',
    date: '12/02/2025',
    time: '1:30 PM - 3:30 PM',
    image: '/class9.png',
    type: 'mobile',
    mentorImage: '/theresa.png'
  },
  {
    id: 10,
    title: 'Server-Side Rendering with Next JS',
    date: '15/02/2025',
    time: '10:30 AM - 12:30 PM',
    image: '/class10.png',
    type: 'frontend',
    mentorImage: '/marvin.png'
  },
  {
    id: 11,
    title: 'Testing Go Applications',
    date: '19/02/2025',
    time: '3:30 PM - 5:30 PM',
    image: '/class11.png',
    type: 'backend',
    mentorImage: '/courtney.png'
  },
  {
    id: 12,
    title: 'Building Native Mobile Apps with Flutter',
    date: '22/02/2025',
    time: '11:30 AM - 1:30 PM',
    image: '/class12.png',
    type: 'mobile',
    mentorImage: '/albert.png'
  }
];

export const getProgramById = (id) => {
  return programs.find(program => program.id === parseInt(id));
};
